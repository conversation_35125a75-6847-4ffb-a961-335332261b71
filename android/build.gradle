buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.0.11718014"
        kotlinVersion = "1.7.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.0')
    }
}

apply plugin: "com.facebook.react.rootproject"
