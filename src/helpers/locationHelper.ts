import { Platform, Alert, PermissionsAndroid } from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import { GOOGLE_PLACES_API_KEY } from '@env';

export interface LocationCoordinates {
  lat: number;
  lng: number;
}

export interface LocationResult {
  coordinates: LocationCoordinates;
  cityName: string;
}

/**
 * Request location permission for Android devices
 * For iOS, permissions are handled automatically by the system
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: "Location Access Required",
          message: "This app needs to access your location",
          buttonPositive: "OK",
          buttonNegative: "Cancel"
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn('Location permission error:', err);
      return false;
    }
  }
  // For iOS, return true as permissions are handled by the system
  return true;
};

/**
 * Extract city name from Google Places address components
 * Prioritizes locality > administrative_area_level_2 > administrative_area_level_1
 */
export const extractCityNameFromAddressComponents = (addressComponents: any[]): string => {
  let cityName = '';
  
  for (const component of addressComponents) {
    if (component.types.includes('locality') || 
        component.types.includes('administrative_area_level_2') ||
        component.types.includes('administrative_area_level_1')) {
      cityName = component.long_name;
      break;
    }
  }
  
  return cityName;
};

/**
 * Get city name from coordinates using Google Geocoding API
 */
export const getCityNameFromCoordinates = async (
  latitude: number, 
  longitude: number
): Promise<string> => {
  const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_PLACES_API_KEY}`;
  
  try {
    const response = await fetch(apiUrl);
    const data = await response.json();
    
    if (data.results && data.results.length > 0) {
      const addressComponents = data.results[0].address_components;
      return extractCityNameFromAddressComponents(addressComponents);
    }
    
    return '';
  } catch (error) {
    console.error('Error fetching city name:', error);
    return '';
  }
};

/**
 * Get current location with permission handling
 * Returns coordinates and city name
 */
export const getCurrentLocationWithPermission = async (): Promise<LocationResult | null> => {
  const hasPermission = await requestLocationPermission();
  
  if (!hasPermission) {
    Alert.alert('Location permission denied');
    return null;
  }

  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      async (info) => {
        const { latitude, longitude } = info.coords;
        const cityName = await getCityNameFromCoordinates(latitude, longitude);
        
        resolve({
          coordinates: {
            lat: latitude,
            lng: longitude
          },
          cityName
        });
      },
      (error) => {
        console.error('Error getting current location:', error);
        Alert.alert('Error', 'Unable to get current location');
        reject(error);
      },
      {
        enableHighAccuracy: false,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  });
};

/**
 * Get current location coordinates only (without city name)
 * Useful when you only need coordinates
 */
export const getCurrentLocation = (): Promise<LocationCoordinates> => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (info) => {
        const { latitude, longitude } = info.coords;
        resolve({
          lat: latitude,
          lng: longitude
        });
      },
      (error) => {
        console.error('Error getting current location:', error);
        reject(error);
      },
      {
        enableHighAccuracy: false,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  });
};

/**
 * Extract city name from Google Places details object
 * Used when user selects a location from GooglePlacesAutocomplete
 */
export const extractCityFromPlaceDetails = (details: any): string => {
  if (details && details.address_components) {
    return extractCityNameFromAddressComponents(details.address_components);
  }
  return '';
};

/**
 * Options for Google Places Autocomplete API
 */
export interface PlaceAutocompleteOptions {
  language?: string;
  country?: string;
}

/**
 * Fetch Google Places Autocomplete predictions for a query string (restricted to cities in India, English only)
 */
export const getPlaceAutocompleteResults = async (query: string): Promise<any[]> => {
  console.log("Places Triggered---------------->", query);
  if (!query || query.trim().length < 1) return [];
  const apiUrl = `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&types=(cities)&components=country:in&language=en&key=${GOOGLE_PLACES_API_KEY}`;
  try {
    const response = await fetch(apiUrl);
    const data = await response.json();
    if (data && data.predictions) {
      console.log("data.predictions---------------->", data.predictions);
      const placeDetails = await getPlaceDetailsById(data.predictions[0].place_id);
      console.log("placeDetails---------------->", placeDetails);
      return data.predictions;
    }
    return [];
  } catch (error) {
    console.error('Error fetching place autocomplete results:', error);
    return [];
  }
};

/**
 * Fetch latitude and longitude for a place_id using Google Place Details API
 */
export const getPlaceDetailsById = async (placeId: string): Promise<{ lat: number; lng: number } | null> => {
  if (!placeId) return null;
  const apiUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_PLACES_API_KEY}`;
  try {
    const response = await fetch(apiUrl);
    const data = await response.json();
    if (data && data.result && data.result.geometry && data.result.geometry.location) {
      return data.result.geometry.location; // { lat, lng }
    }
    return null;
  } catch (error) {
    console.error('Error fetching place details:', error);
    return null;
  }
};
