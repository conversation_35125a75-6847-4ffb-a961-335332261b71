import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import { useNavigation } from '@react-navigation/native';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { Alert } from 'react-native';
const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userId, setUserId] = useState(null);
  const [userToken, setUserToken] = useState(null);
  const [selectedFilters, setSelectedFilters]  = useState(null);
  const [playerData, setPlayerData] = useState({});
  const navigation = useNavigation();

  // Restore login state from AsyncStorage on mount
  useEffect(() => {
    const checkLoginState = async () => {
      try {
        const token = await AsyncStorage.getItem('userToken');
        const id = await AsyncStorage.getItem('userId');
        if (token && id) {
          setIsLoggedIn(true);
          setUserToken(token);
          setUserId(id);
        }
      } catch (error) {
        console.error("Error loading auth state:", error);
      }
    };
    checkLoginState();
  }, []);

  const applyFilter = filters => {
    setSelectedFilters(filters);
    setFilterVisible && setFilterVisible(false);
  };

  const login = async(id, token) => {
    setIsLoggedIn(true);
    setUserId(id);
    setUserToken(token);
    await AsyncStorage.setItem('userToken', token);
    await AsyncStorage.setItem('userId', id);
  };

  const clearStorage = async () => {
    await AsyncStorage.clear();
  };

  const logout = async () => {
    setIsLoggedIn(false);
    setUserId(null);
    setUserToken(null);
    setPlayerData({});
    await clearStorage();
    await GoogleSignin.signOut();
  };

  const fetchUserDetails = async (token, id) => {
    try {
      const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/player/${id}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });
      if(response.status === 401)
      {
        logout();
        Alert.alert("Status", "Please Sign Again", [
          {
            text: "OK", onPress: () => {
              navigation.navigate("SignIn")
            }
          }
        ]);
      }else{
        const result = await response.json();
        setPlayerData(result);
        setIsLoggedIn(true);
      }
    } catch (error) {
      console.error("Error fetching user details:", error);
    }
  };

  useEffect(() => {
    if (isLoggedIn && userToken && userId) {
      fetchUserDetails(userToken, userId);
    }
  }, [isLoggedIn, userToken, userId]);

  return (
    <AuthContext.Provider value={{ isLoggedIn, login, logout, userId, userToken, applyFilter, selectedFilters, setSelectedFilters, playerData, fetchUserDetails }}>
      {children}
    </AuthContext.Provider>
  );
};
