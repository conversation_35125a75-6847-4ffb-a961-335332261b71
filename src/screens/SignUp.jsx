import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  ScrollView,
  Alert,
  Modal,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigation } from '@react-navigation/native';
import { NEXT_PUBLIC_BASE_URL, webClientId, androidClientId } from '@env';
import { useAuth } from '../Context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Country, State, City } from 'country-state-city';


import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';
GoogleSignin.configure({
  webClientId: webClientId,
  androidClientId: androidClientId,
  // iosClientId: GOOGLE_IOS_CLIENT_ID,
  scopes: ['profile', 'email'],
});
import axios from 'axios';
const GoogleLogin = async () => {
  await GoogleSignin.hasPlayServices();
  const userInfo = await GoogleSignin.signIn();
  return userInfo;
};

const SignUp = () => {
  const navigation = useNavigation();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { login, userId, userToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [states, setStates] = useState([]);
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const handleGoogleLogin = async () => {
    if(loading) return;
    setLoading(true);
    try {
      const response = await GoogleLogin();
      const { idToken, user } = response;

      if (idToken && user) {
        const myHeaders = new Headers();
        myHeaders.append('Content-Type', 'application/json');

        const raw = JSON.stringify({
          email: user.email,
          firstName: user.givenName,
          lastName: user.familyName,
        });

        const requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow',
        };
        const response = await fetch(
          `${NEXT_PUBLIC_BASE_URL}/api/player/google`,
          requestOptions,
        );
        const result = await response.json();
        const token = result?.token;
        const id = result?.id;
        if (result?.status === 'ok') {
          await login(id, token);
          navigation.navigate('Home');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(
        error?.response?.data?.error?.message ||
        'Something went wrong during Google login.',
      );
    } finally {
      setLoading(false);
    }
  };

  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  const phoneRegExp =
    /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;
  const signUpSchema = Yup.object().shape({
    firstName: Yup.string()
      .trim()
      .min(3, 'First name must be at least 3 characters')
      .max(15, 'Cannot exceed 15 characters')
      .required('Please enter your first name'),
    lastName: Yup.string()
      .trim()
      .min(3, 'Last name must be at least 3 characters')
      .max(15, 'Cannot exceed 15 characters')
      .required('Please enter your last name'),
    mobile: Yup.string()
      .min(10, 'Phone number must be 10 characters long')
      .max(10, 'Phone number must be 10 characters long')
      .matches(phoneRegExp, 'Phone number is not valid')
      .required('Please enter your phone number'),
    email: Yup.string()
      .email('Please enter a valid email address')
      .matches(emailRegExp, 'Please enter a valid email address')
      .required('Please enter your email address'),
    homeState: Yup.string()
      .required("Home State is required")
      .max(100, "Only 100 characters are allowed"),
    school: Yup.string()
      .min(4, 'School must be at least 4 characters')
      .max(50, 'Cannot exceed 50 characters'),
    // grade: Yup.string(),
    password: Yup.string()
      .matches(/^(?=\S+$).*/, 'Password cannot contain spaces')
      .min(8, 'Password must be at least 8 characters')
      .required('Please enter your password'),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password'), null], 'Passwords must match')
      .required('Please confirm your password'),
  });

  const initialValues = {
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
    school: '',
    homeState: "",
    hobbies: [],
    password: '',
    
    confirmPassword: '',
  };
  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: signUpSchema,

    onSubmit: async values => {
      const userObj = {
        firstName: values.firstName,
        lastName: values.lastName,
        mobile: values.mobile,
        email: values.email,
        homeState: values.homeState,
        schoolName: values.school,
        hobbies: selectedHobbies.map(id => ({ id })),
        password: values.password,
        confirmPassword: values.confirmPassword,
      };

      try {
        const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/player`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userObj),
        });

        const data = await response.json();
        if (response.ok) {
          await login(data.data._id, data.token);
          Alert.alert('Success', 'You have signed up successfully.');
          navigation.navigate('Home');
        } else {
          if (data && data.message) {
            Alert.alert('Signup Failed', data.error);
          } else if (data && data.error) {
            Alert.alert('Signup Failed', data.error);
          } else {
            Alert.alert(
              'Signup Failed',
              'An error occured during sign up. Please try again!',
            );
          }
        }
      } catch (error) {
        console.error('Signup error:', error);
        Alert.alert('Signup Failed', 'An error occurred during sign up. Please try again!');
      }
    },
  });
  const toggleShowPassword = () => setShowPassword(!showPassword);
  const toggleShowConfirmPassword = () =>
    setShowConfirmPassword(!showConfirmPassword);

  const [hobbies, setHobbies] = useState([]);
  const [selectedHobbies, setSelectedHobbies] = useState([]);
  const [selectedState, setSelectedState] = useState('')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [isStateDropDown, setIsStateDropDown] = useState(false);

  useEffect(() => {
    // Fetch the options
    fetch(`${NEXT_PUBLIC_BASE_URL}/api/category`)
      .then(response => response.json())
      .then(data => setHobbies(data.data))
      .catch(error => console.error('Failed to load hobbies', error));
  }, []);
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };
  const toggleStateDropdown = () => {
    setIsStateDropDown(!isStateDropDown); // Toggle the state dropdown visibility
  };
  const handleSelectState = (stateCode) => {
    setSelectedState(stateCode);
    formik.setFieldValue('homeState', stateCode); // Update Formik value

    toggleStateDropdown();
  };
  const handleSelectHobby = hobbyId => {
    const isAlreadySelected = selectedHobbies.includes(hobbyId);
    if (isAlreadySelected) {
      setSelectedHobbies(selectedHobbies.filter(id => id !== hobbyId));
    } else {
      setSelectedHobbies([...selectedHobbies, hobbyId]);
    }
  };
  const screenWidth = Dimensions.get('window').width;
  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
      <SafeAreaView>
        <ScrollView>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.container}>
              <View style={styles.logoContainer}>
                <Image
                  source={require('../../src/assets/MainKhelCoach.png')}
                  alt="HeaderLogo"
                  style={styles.headerLogo}
                />
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.title}>Sign up for your account</Text>
              </View>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>First Name<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                <TextInput
                  style={[
                    styles.input,
                    formik.touched.firstName &&
                    formik.errors.firstName &&
                    styles.inputError,
                  ]}
                  placeholder="Enter First Name"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.firstName}
                  onChangeText={formik.handleChange('firstName')}
                  onBlur={formik.handleBlur('firstName')}
                />
                <Text style={styles.errorText}>
                  {formik.touched.firstName && formik.errors.firstName}
                </Text>

                <Text style={styles.label}>Last Name<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                <TextInput
                  z
                  style={[
                    styles.input,
                    formik.touched.lastName &&
                    formik.errors.lastName &&
                    styles.inputError,
                  ]}
                  placeholder="Enter Last Name"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.lastName}
                  onChangeText={formik.handleChange('lastName')}
                  onBlur={formik.handleBlur('lastName')}
                />
                <Text style={styles.errorText}>
                  {formik.touched.lastName && formik.errors.lastName}
                </Text>

                <Text style={styles.label}>Enter Email Address<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                <TextInput
                  style={[
                    styles.input,
                    formik.touched.email &&
                    formik.errors.email &&
                    styles.inputError,
                  ]}
                  placeholder="Enter Email Address"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.email}
                  onChangeText={text => formik.setFieldValue('email', text.toLowerCase())}
                  onBlur={formik.handleBlur('email')}
                  keyboardType="email-address"
                />
                <Text style={styles.errorText}>
                  {formik.touched.email && formik.errors.email}
                </Text>

                <Text style={styles.label}>Enter Mobile Number<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderWidth: 1,
                  borderColor: '#9CA3AF',
                  borderRadius: 5,
                }}>
                  <View style={{
                    paddingHorizontal: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRightWidth: 1,
                    borderRightColor: '#9CA3AF',
                  }}>
                    <Text style={{ fontSize: 16, color:"#000"}}>+91</Text>
                  </View>
                  <TextInput
                    style={[
                      styles.input,
                      formik.touched.mobile && formik.errors.mobile && styles.inputError,
                    ]}
                    placeholder="Enter Mobile Number"
                    placeholderTextColor="#9CA3AF"
                    value={formik.values.mobile}
                    onChangeText={formik.handleChange('mobile')}
                    onBlur={formik.handleBlur('mobile')}
                    keyboardType="numeric"
                  />
                </View>
                {/* <TextInput
                  style={[
                    styles.input,
                    formik.touched.mobile &&
                    formik.errors.mobile &&
                    styles.inputError,
                  ]}
                  placeholder="Enter Mobile Number"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.mobile}
                  onChangeText={formik.handleChange('mobile')}
                  onBlur={formik.handleBlur('mobile')}
                  keyboardType="numeric"
                /> */}
                <Text style={styles.errorText}>
                  {formik.touched.mobile && formik.errors.mobile}
                </Text>

                <Text style={styles.label}>School/College</Text>
                <TextInput
                  style={[
                    styles.input,
                    formik.touched.school &&
                    formik.errors.school &&
                    styles.inputError,
                  ]}
                  placeholder="Enter School/College"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.school}
                  onChangeText={formik.handleChange('school')}
                  onBlur={formik.handleBlur('school')}
                />
                <Text style={styles.errorText}>
                  {formik.touched.school && formik.errors.school}
                </Text>


                <View>
                  <Text style={styles.label}>Your Home State<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                  <TouchableOpacity onPress={toggleStateDropdown}>
                    <TextInput
                      style={styles.input}
                      editable={false}
                      placeholder="Select State"
                      placeholderTextColor="#9CA3AF"
                      value={
                        selectedState
                          ? states.find(state => state.isoCode === selectedState)?.name
                          : ''
                      }
                    />
                  </TouchableOpacity>

                  <Modal visible={isStateDropDown}
                    animationType="slide"
                    transparent={true}>
                    <View style={styles.inputContainerHobbies}>
                      <View style={styles.modalContainer}>
                        <ScrollView contentContainerStyle={styles.scrollView}>
                          {states.map(state => (
                            <TouchableOpacity
                              key={state.isoCode}
                              style={styles.option}
                              onPress={() => handleSelectState(state.isoCode)}>
                              <Text style={styles.optionText}>
                                {state.name}{' '}
                                {selectedState === state.isoCode ? '✓' : ''}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                        <TouchableOpacity onPress={toggleStateDropdown} style={styles.closeButton}>
                          <Text style={{ color: '#fff', fontSize: 14 }}>Close</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </Modal>

                  <Text style={styles.errorText}>
                    {formik.touched.homeState && formik.errors.homeState}
                  </Text>
                </View>


                <Text style={styles.label}>Hobbies</Text>
                <View>
                  <TouchableOpacity onPress={toggleDropdown}>
                    <TextInput
                      style={styles.input}
                      editable={false}
                      placeholder="Select Hobbies"
                      placeholderTextColor="#9CA3AF"
                      value={
                        selectedHobbies.length > 0
                          ? selectedHobbies
                            .map(
                              hobbyId =>
                                hobbies.find(hobby => hobby._id === hobbyId)
                                  ?.name,
                            )
                            .join(', ')
                          : ''
                      }
                    />
                  </TouchableOpacity>

                  <Modal
                    visible={isDropdownOpen}
                    animationType="slide"
                    transparent={true}>
                    <View style={styles.inputContainerHobbies}>
                      <View style={styles.modalContainer}>
                        <ScrollView contentContainerStyle={styles.scrollView}>
                          {hobbies.map(hobby => (
                            <TouchableOpacity
                              key={hobby._id}
                              style={styles.option}
                              onPress={() => handleSelectHobby(hobby._id)}>
                              <Text style={styles.optionText}>
                                {hobby.name}{' '}
                                {selectedHobbies.includes(hobby._id) ? '✓' : ''}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                        <TouchableOpacity
                          onPress={toggleDropdown}
                          style={styles.closeButton}>
                          <Text style={{ color: '#fff', fontSize: 14 }}>
                            Close
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </Modal>
                </View>

                <Text style={styles.errorText}>
                  {formik.touched.hobbies && formik.errors.hobbies}
                </Text>

                {/* <Text style={styles.label}>Grade</Text>
                <TextInput
                  style={[styles.input, formik.touched.grade && formik.errors.grade && styles.inputError]}
                  placeholder="Enter Grade"
                  placeholderTextColor="#9CA3AF"
                  value={formik.values.grade}
                  onChangeText={formik.handleChange('grade')}
                  onBlur={formik.handleBlur('grade')}
                /> */}
                {/* <Text style={styles.errorText}>{formik.touched.grade && formik.errors.grade}</Text> */}

                <View style={{ position: 'relative' }}>
                  <Text style={styles.label}>Enter Password<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                  <TextInput
                    style={[
                      styles.input,
                      formik.touched.password &&
                      formik.errors.password &&
                      styles.inputError,
                    ]}
                    placeholder="Enter Password"
                    placeholderTextColor="#9CA3AF"
                    value={formik.values.password}
                    onChangeText={formik.handleChange('password')}
                    onBlur={formik.handleBlur('password')}
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    onPress={() => setShowPassword(!showPassword)}
                    style={{ position: 'absolute', top: 38, right: 10 }}>
                    <Image
                      source={
                        showPassword
                          ? require('../../src/assets/eye.png')
                          : require('../../src/assets/eye-crossed.png')
                      }
                      style={styles.eyeIcon}
                    />
                  </TouchableOpacity>
                </View>
                <Text style={styles.errorText}>
                  {formik.touched.password && formik.errors.password}
                </Text>
                <Text style={styles.label}>Confirm Password<Text style={{color: 'red', marginLeft: 2}}>*</Text></Text>
                <View style={{ position: 'relative' }}>
                  <TextInput
                    style={[
                      styles.input,
                      formik.touched.confirmPassword &&
                      formik.errors.confirmPassword &&
                      styles.inputError,
                    ]}
                    placeholder="Confirm Password"
                    placeholderTextColor="#9CA3AF"
                    value={formik.values.confirmPassword}
                    onChangeText={formik.handleChange('confirmPassword')}
                    onBlur={formik.handleBlur('confirmPassword')}
                    secureTextEntry={!showConfirmPassword}
                  />
                  <TouchableOpacity
                    onPress={toggleShowConfirmPassword}
                    style={{ position: 'absolute', top: 15, right: 10 }}>
                    <Image
                      source={
                        showConfirmPassword
                          ? require('../../src/assets/eye.png')
                          : require('../../src/assets/eye-crossed.png')
                      }
                      style={styles.eyeIcon}
                    />
                  </TouchableOpacity>
                </View>
                <Text style={styles.errorText}>
                  {formik.touched.confirmPassword &&
                    formik.errors.confirmPassword}
                </Text>

                <View>
                  <TouchableOpacity
                    style={styles.signUpButton}
                    onPress={formik.handleSubmit}>
                    <Text style={styles.signUpButtonText}>Sign Up</Text>
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.orContinueContainer}>
                <View style={styles.orLine}></View>
                <Text style={styles.orText}>Or continue with</Text>
                <View style={styles.orLine}></View>
              </View>
            
                <TouchableOpacity
                  activeOpacity={0.8}
                  style={{ width: '100%', alignItems: 'center', marginTop: 20 }}
                  onPress={handleGoogleLogin}
                >
                  <GoogleSigninButton
                    style={{ width: '100%', height: 48, flex: 1 }}
                    size={GoogleSigninButton.Size.Wide}
                    color={GoogleSigninButton.Color.Dark}
                  />
                </TouchableOpacity>
    
              <View style={styles.signInCon}>
                <Text>
                  <View>
                    <Text style={styles.alReadymem}> Already member?</Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('SignIn')}>
                    <Text style={styles.signInText}> Sign In</Text>
                  </TouchableOpacity>
                </Text>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: '5%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
    margin: '3%',
    padding: '5%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 1,
    overflow: 'scroll',
  },
  logoContainer: {
    marginBottom: '5%',
  },
  headerLogo: {
    width: 200,
    height: 100,
    resizeMode: 'contain',
  },
  textContainer: {
    marginBottom: '5%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: '7%',
    color: '#000',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    // marginBottom: '3%',
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    width: '100%',
    color: '#000',
  },
  inputError: {
    borderColor: 'red',
  },
  label: {
    fontSize: 14,
    marginTop: '2%',
    // marginBottom: '2%',
    color: '#000',
  },
  eyeIcon: {
    width: 24,
    height: 24,
  },
  signUpButton: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    height: 50,
    marginTop: '5%',
  },
  signUpButtonText: {
    color: '#FAFBFCff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
  signInCon: {
    marginTop: '5%',
  },
  orContinueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '7%',
    marginBottom: '1%',
  },
  orLine: {
    flex: 1,
    height: 1,
    opacity: 0.5,
    backgroundColor: 'grey',
    marginLeft: 5,
    marginRight: 5,
  },
  orText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'grey',
  },
  googleSignInButton: {
    flexDirection: 'row',
    backgroundColor: '#FAFBFCFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  googleSignInButtonText: {
    marginLeft: 10,
    color: '#000',
    fontSize: 16,
  },
  googleLogo: {
    width: 24,
    height: 24,
  },
  alReadymem: {
    fontSize: 14,
    alignItems: 'center',
    color: '#000',
  },
  signInText: {
    color: '#0EA5E9',
    fontWeight: '600',
  },
  errorText: {
    color: 'red',
  },
  // modalOverlay: {
  //   flex: 1,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   backgroundColor: 'rgba(0, 0, 0, 0.5)',
  // },
  inputContainerHobbies: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: '15%',
    width: '92%',
    maxHeight: '80%',
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: 10,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '3%',
  },
  scrollView: {
    flexDirection: 'column',
    marginVertical: '2%',
  },
});

export default SignUp;
