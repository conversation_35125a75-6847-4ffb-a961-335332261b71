import React, { useEffect } from "react";
import { SafeAreaView } from "react-native";
import { useRoute, RouteProp } from "@react-navigation/native";
import { styles } from "./styles";
import SearchUpdated from "../../components/SearchUpdated/SearchUpdated";


interface SearchScreenParams {
  locationObj?: {
    lat: number | null;
    lan: number | null;
  };
  cityName?: string;
}

type SearchScreenRouteProp = RouteProp<
  { SearchScreen: SearchScreenParams },
  'SearchScreen'
>;

const SearchScreen = ({navigation, route}: any) => {
  const { locationObj, cityName } = route.params || {};

  // Handle course card press
  const handleCoursePress = (course: any) => {
    navigation.navigate('Courses', { courseId: course._id });
  };

  // Handle coach card press
  const handleCoachPress = (coach: any) => {
    console.log('Coach selected:', coach);
    navigation.navigate('CoachProfile', { ID: coach._id });
  };

  // useEffect(()=>{
  //   console.log("Search Screen Route Changed: ", cityName, locationObj);
  // },[navigation, route.params])

  return (
    <SafeAreaView style={styles.safeAreaContainer}>
      <SearchUpdated
        key={JSON.stringify(locationObj)}
        initialLocationObj={locationObj}
        initialCityName={cityName}
        onCoursePress={handleCoursePress}
        onCoachPress={handleCoachPress}
        showLocationSelector={true}
      />
    </SafeAreaView>
  );
};



export default SearchScreen;
