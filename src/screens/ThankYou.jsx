import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, ActivityIndicator, } from 'react-native';
import Footer from '../components/footer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native'
import Search from '../components/Search';
import { NEXT_PUBLIC_BASE_URL, RAZORPAY_KEY } from '@env';
import axios from 'axios';
import { useAuth } from '../Context/AuthContext';

import LinearGradient from 'react-native-linear-gradient';
const confirmationIcon = require('../../src/assets/Confirmation.png');

const ThankYou = () => {
  const route = useRoute();
  const [isLoading, setIsLoading] = useState(true);
  const { login, setUser, isLoggedIn, userToken } = useAuth();
  const getBookingId = route.params.BookingId;
  const [thankyouDetails, setThankyouDetails] = useState();
  const navigation = useNavigation();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const requestOptions = {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
        };
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/booking/${getBookingId}`, requestOptions);
        setThankyouDetails(response);
      } catch (error) {
        console.error(error);
      }
      finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [getBookingId]);
  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { day: "numeric", month: "long", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };
  const formatTime = (timeString) => {
    if (!timeString) {
      return "";
    }
    const [hours, minutes] = timeString.split(":");
    const time = new Date(1970, 0, 1, hours, minutes);
    return time.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
    });
  };

  let subtotal = thankyouDetails?.data?.classes.reduce((accu, x) => accu + x.fees, 0)
  subtotal = subtotal / 1.18;
  const coachGst = thankyouDetails?.data?.coachId?.hasGst ? subtotal * 0.18 : 0;
  const platformTax = subtotal * 0.12
  const taxGST = platformTax * 0.18
  const total = Math.ceil(subtotal + platformTax + taxGST + coachGst)
  const bookingDate = new Date(thankyouDetails?.data?.createdAt.split("T")[0]).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
  return (
    <ScrollView>
      <View style={styles.mainContainer}>
        {/* <Search /> */}
        <LinearGradient
          colors={['rgba(227, 31, 38, 0.1)', 'rgba(0, 190, 439, 0.5)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.linearGradient}
        >
          <View style={styles.section}>
            <View style={styles.columns}>
              <View style={styles.confirmationSection}>
                <Text style={styles.thankYouText}>A GREAT BIG THANK YOU!</Text>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', paddingHorizontal: 10 }}>
                  <TouchableOpacity onPress={() => navigation.navigate("Invoice", { BookingId: getBookingId })}>
                    <Text style={{ color: "#0EA5E9", marginHorizontal: 10 }}>Platform Invoice</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => navigation.navigate("CoachInvoice", { BookingId: getBookingId })}>
                    <Text style={{ color: "#0EA5E9", marginHorizontal: 10 }}>Coach Invoice</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => navigation.navigate("OrderSummary", { BookingId: getBookingId })}>
                    <Text style={{ color: "#0EA5E9", marginHorizontal: 10 }}>Order Summary</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.paymentConfirmation}>
                  <Image source={confirmationIcon} style={styles.confirmationIcon} />
                  <View>
                    <Text style={styles.confirmationText}>Payment Confirmed!</Text>
                    <Text style={styles.confirmationSubtext}>Ordered on {bookingDate} | Booking id: {thankyouDetails?.data?.bookingId}</Text>
                  </View>
                </View>
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Details</Text>
                <View style={styles.row}>
                  <Text style={styles.label}>Course Name:</Text>
                  <Text style={styles.value}>{thankyouDetails?.data?.courseId?.courseName}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Coach Name:</Text>
                  <Text style={styles.value}>{thankyouDetails?.data?.coachId?.firstName} {thankyouDetails?.data?.coachId?.lastName}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Venue:</Text>
                  <Text style={styles.value}>{thankyouDetails?.data?.courseId?.facility?.addressLine1}{", "}{thankyouDetails?.data?.courseId?.facility?.addressLine2}{", "}{thankyouDetails?.data?.courseId?.facility?.city}{", "}{thankyouDetails?.data?.courseId?.facility?.state}{", "}{thankyouDetails?.data?.courseId?.facility?.pinCode}</Text>
                </View>
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Payment Details</Text>
                <View style={styles.row}>
                  <Text style={styles.label}>Payment Method:</Text>
                  <Text style={styles.value}>
                    {thankyouDetails?.data?.paymentMode && thankyouDetails?.data?.wallet
                      ? `${thankyouDetails?.data?.paymentMode} & Wallet`
                      : thankyouDetails?.data?.paymentMode
                        ? thankyouDetails?.data?.paymentMode.toUpperCase()
                        : "Wallet"}
                  </Text>
                </View>
                {thankyouDetails?.data?.paymentId && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Payment id:</Text>
                    <Text style={styles.value}>{thankyouDetails?.data?.paymentId}</Text>
                  </View>


                )}
                <View style={styles.row}>
                  <Text style={styles.label}>Transaction Id:</Text>
                  <Text style={styles.value}>{thankyouDetails?.data?.razorPayPaymentId ? thankyouDetails?.data?.razorPayPaymentId?.toUpperCase() : thankyouDetails?.data?.bookingId}</Text>
                </View>
                {thankyouDetails?.data?.wallet && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Wallet Amount:</Text>
                    <Text style={styles.value}>{thankyouDetails?.data?.walletAmount.toFixed(2)}</Text>
                  </View>


                )}
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Booking Details</Text>
                <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
                  {thankyouDetails?.data?.classes?.map((x, index) => (
                    <View style={[styles.bookingDateRow, { alignItems: "center", width: "80%", marginVertical: "1%", maxHeight: 100 }]} key={index}>
                      <View style={styles.row}>
                        <Text style={styles.label}>Dates :</Text>
                        <Text style={styles.value}>
                          {`${new Date(x.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' })} (${x?.days})`}
                        </Text>
                      </View>
                      <View style={styles.row}>
                        <Text style={styles.label}>Time :</Text>
                        <Text style={styles.value}>
                          {`${formatTime(x.startTime)} - ${formatTime(x.endTime)}`}
                        </Text>
                      </View>
                    </View>
                  ))}
                </ScrollView>

              </View>
            </View>

            <View style={styles.paymentRow}>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>Subtotal ({thankyouDetails?.data?.courseType})</Text>
                <Text style={styles.totalAmount}>₹ {subtotal?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>Platform Fee (12%)</Text>
                <Text style={styles.totalAmount}>₹ {platformTax?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>GST (18%)</Text>
                <Text style={styles.totalAmount}>₹ {taxGST?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>CoachGST (18%)</Text>
                <Text style={styles.totalAmount}>₹ {coachGst?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalLabel}>₹ {total.toFixed(2)}</Text>
              </View>
            </View>
            <View style={styles.horizontalLine} />
            {/* {renderSocialIcons()} */}
          </View>


        </LinearGradient>
        <Footer />
      </View>
    </ScrollView>
  );
};

export default ThankYou;

const styles = StyleSheet.create({
  linearGradient: {
    flex: 1,
  },
  container: {
    paddingHorizontal: "1%"
  },
  section: {
    width: "90%",
    marginVertical: "5%",
    marginHorizontal: "5%",
    backgroundColor: "#F9FAFB",
    borderRadius: 10,
  },
  columns: {
    justifyContent: "center",
    alignItems: "center",
  },
  subSection: {
    width: "90%",
    marginVertical: "4%",
    borderRadius: 5,
    backgroundColor: '#fff',
    borderWidth: 0.8,
    borderColor: '#ccc',
    paddingHorizontal: "3%",
    paddingVertical: "3%"
  },
  thankYouText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    color: '#000',
    marginVertical: "3%",
    letterSpacing: 1,

  },
  confirmationSection: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: "3%",
  },
  paymentConfirmation: {
    flexDirection: 'row',
    alignItems: 'center',
    width: "90%"
  },
  confirmationIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  confirmationText: {
    fontSize: 15,
    color: '#0EA5E9',

  },
  confirmationSubtext: {
    fontSize: 14,
    color: '#000',
    lineHeight: 16
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 5,
  },
  detailsLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    textAlign: 'left',
    color: "black"
  },
  label: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: 'black',
  },
  value: {
    flex: 2,
    fontSize: 14,
    color: 'black',
    textAlign: 'left',
  },

  paymentRow: {
    paddingHorizontal: "3%",

  },
  amounRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: "3%",
  },
  logo: {

  },
  paymentText: {
    fontSize: 15,
    color: "grey",
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    color: "grey"

  },
  totalLabel: {
    fontSize: 16,
    color: "#000",
    fontWeight: "500"
  },
  totalAmount: {
    fontSize: 16,
    color: "grey",

  },
  horizontalLine: {
    backgroundColor: '#BCBEC0',
    height: 1,
    width: '90%',
    alignSelf: 'center',
    marginVertical: "4%"
  },
  shareSection: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,

  },
  shareHeading: {
    fontSize: 16,
    marginBottom: 10,
    color: "#000",
  },
  socialIconsContainer: {
    alignItems: 'center',
    margin: 10,
  },
  socialIcon: {
    width: 20,
    height: 20,
    // marginHorizontal: 10,
  },
  scrollView: {
    flexDirection: 'column',
    // alignItems: 'center',
    // marginVertical: "1%"
  },
});
