import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Button,
  Image,
  FlatList,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import HTML from 'react-native-render-html';

import { useRoute } from '@react-navigation/native';
import { useAuth } from '../Context/AuthContext';
import { NEXT_PUBLIC_BASE_URL, COACH_ID_TOKEN } from '@env';
import { useNavigation } from '@react-navigation/native';
import axios from 'axios';
import Search from '../components/Search';
import Footer from '../components/footer';
import SearchLocationContainer from '../components/SearchLocationContainer/SearchLocationContainer';

const { width } = Dimensions.get('window');
const SIDE_PADDING = 30;
const sliderWidth = width - 2 * SIDE_PADDING;
const IMAGE_HORIZONTAL_PADDING = 16;


const BookingDetails = () => {
  const route = useRoute();

  const { bookingId } = route.params;
  const [bookingData, setBookingData] = useState();
  const { login, setUser, isLoggedIn , userToken} = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [datesModal, setDatesModal] = useState(false);
  const navigation = useNavigation();
  useEffect(() => {
    setIsLoading(true);
    const requestOptions = {
      method: 'GET',
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userToken}`,
      },
      redirect: 'follow',
    };

    fetch(`${NEXT_PUBLIC_BASE_URL}/api/booking/${bookingId}`, requestOptions)
      .then(response => response.json())
      .then(result => {
        setIsLoading(false);
        setBookingData(result);
      })
      .catch(error => console.error(error));
  }, [bookingId]);

  const tagsStyles = {
    p: { fontSize: 16, color: '#000', marginVertical: 0 }, // Style for <p> tag
    // Add more styles for other tags as needed
  };
  const bookingDate = new Date(bookingData?.createdAt.split("T")[0]).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
  // Determine the class type text and status text with colors
  const classTypeText = bookingData?.courseId?.classType === 'class' ? 'Session' : bookingData?.courseId?.classType;
  const statusText = bookingData?.status === 'Active' ? 'Active' : bookingData?.status === 'Inactive' ? 'Inactive' : bookingData?.status;
  const statusColor = bookingData?.status === 'Active' ? '#4CAF50' : bookingData?.status === 'InActive' ? 'red' : 'black';

  const toggleDatesModal = () => {
    setDatesModal(!datesModal);
  };

  bookingData?.classes?.map((bookingTime, index) => {
    console.log(`Booking Time Status for index ${index}:`, bookingTime.status);
    // Add your processing logic here if needed
});
  return (
    <ScrollView>
      <SearchLocationContainer />
      <View style={styles.card}>
        <View style={styles.nameStatus}>
          <Text style={{ color: "#000", fontSize: 16 }}>{bookingData?.courseId?.courseName}</Text>

          <View style={styles.status}>
            <Text style={{ color: statusColor, marginLeft: "1%", borderColor: statusColor, borderWidth: 1, borderRadius: 5, padding: "1%", fontSize: 12, }}>{statusText}</Text>
          </View>
        </View>
        <Image
          source={{ uri: bookingData?.courseId?.images[0].url }}
          style={styles.bookingImage}
        />
        <View style={{ flex: 1, flexDirection: 'column', justifyContent: 'space-between', marginVertical: "2%" }}>
          <TouchableOpacity onPress={toggleDatesModal}>
            <View
              style={{
                width: "100%",
                height: 35,
                borderRadius: 3,
                backgroundColor: "#4CAF50",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginVertical: "2%"
              }}
            >
              <Text style={{ fontSize: 14, color: "#fff" }}>
                Show Booking Dates
              </Text>
            </View>
          </TouchableOpacity>
          <View style={{ borderRadius: 8, borderWidth: 1, padding: "3%", width: "100%" }}>
            <View style={styles.row}>
              <Text style={styles.textCon}>Coach Details :</Text>
              <TouchableOpacity onPress={() => {
                    navigation.navigate('CoachProfile', { ID:bookingData?.coachId?._id })
                  }}>
                <Text style={{ color: '#007bff' }} >
                  {bookingData?.coachId?.firstName ? `${bookingData?.coachId?.firstName} ${bookingData?.coachId?.lastName}` : bookingData?.courseId?.coachName}
                </Text>
              </TouchableOpacity>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Price :</Text>
              <Text style={styles.textStyle}>₹{bookingData?.pricePaid}</Text>
              <Text style={{ fontSize: 13, color: '#888' }}> (inclusive all taxes)</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Booking Date :</Text>
              <Text style={styles.valueWith}>{bookingDate}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Booking ID :</Text>
              <Text style={styles.valueWith}>{bookingData?._id}</Text>
            </View>
            <View style={styles.row}>
              <Text style={styles.textCon}>Venue :</Text>
              <Text style={styles.valueWith}> {bookingData?.courseId?.facility?.addressLine1}, {bookingData?.courseId?.facility?.addressLine2}, {bookingData?.courseId?.facility?.city}, {bookingData?.courseId?.facility?.country}</Text>
            </View>
            <View style={[styles.row, styles.needToCarry]}>
              <Text style={styles.textCon}>Need to Carry :</Text>
              {/* <Text  style={styles.textStyle} dangerouslySetInnerHTML={{ __html: bookingData?.courseId?.whatYouHaveToBring }}></Text> */}
              <HTML tagsStyles={{
                textCon: { color: "#000", width: "70%", fontFamily: 'Lato-Regular', },
                p: { margin: 0, padding: 0, color: "#000", fontFamily: "Lato-Regular" },
                h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
                br: { display: 'none' },
                li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
                ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
              }} source={{ html: bookingData?.courseId?.whatYouHaveToBring }} />
            </View>
          </View>
        </View>
        <TouchableOpacity onPress={()=>navigation.navigate("Invoice",  {BookingId: bookingId })}>
            <View
              style={{
                width: "100%",
                height: 35,
                borderRadius: 3,
                backgroundColor: "#0EA5E9",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginVertical: "2%"
              }}
            >
              <Text style={{ fontSize: 14, color: "#fff" }}>
                Download Platform Invoice
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={()=>navigation.navigate("CoachInvoice",  {BookingId: bookingId })}>
            <View
              style={{
                width: "100%",
                height: 35,
                borderRadius: 3,
                backgroundColor: "#0EA5E9",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginVertical: "2%"
              }}
            >
              <Text style={{ fontSize: 14, color: "#fff" }}>
                Download Coach Invoice
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity onPress={()=>navigation.navigate("OrderSummary",  {BookingId: bookingId })}>
            <View
              style={{
                width: "100%",
                height: 35,
                borderRadius: 3,
                backgroundColor: "#0EA5E9",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginVertical: "2%"
              }}
            >
              <Text style={{ fontSize: 14, color: "#fff" }}>
                Download Order Summary
              </Text>
            </View>
          </TouchableOpacity>
      </View>
      <Modal visible={datesModal} animationType="slide" transparent>
        <View style={styles.modalOverlay}>
          <View style={styles.modalView}>
            <Text
              style={{ fontSize: 18, fontWeight: "500", marginBottom: "2%", color: "#000",  fontFamily: 'Lato-Bold' }}
            >
              Your Booking Dates
            </Text>
            <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
              {bookingData?.classes?.map((bookingTime, index) => {
                const date = new Date(bookingTime?.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
                const endDate = new Date(bookingTime.endDate).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
                return (
                  bookingData?.courseId?.classType === 'class' ? (
                    <View key={index} style={{ flexDirection: 'row', alignItems: 'center',  width: "90%" }}>
                      {/* <Text style={{ width: 100, color: 'black' }}>Booking Slot :</Text> */}
                      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                        <Text style={styles.dateFont}>{date}</Text>
                        <Text style={styles.dateFont}>|</Text>
                        <Text style={styles.dateFont}>{bookingTime.startTime} - {bookingTime.endTime}</Text>
                        {/* <Text style={{ fontWeight: 'bold', color: '#888' }}> ({bookingTime?.duration})</Text> */}
                        <View style={{ width: "90%" , marginLeft:"1%" }}><Text style={{ color: "#000", fontSize: 14, lineHeight: 20 }}>
                             <Text style={{ color: bookingTime?.status === "upcoming" ? "green" : "red" }}>({bookingTime?.status})</Text>
                          </Text></View>
                      </View>
                      <View style={{ width: "90%", marginLeft:"1%" }}><Text style={{ color: "#000", fontSize: 14, lineHeight: 20 }}>
                             <Text style={{ color: bookingTime?.status === "upcoming" ? "green" : "red" }}>({bookingTime?.status})</Text>
                          </Text></View>
                    </View>
                  ) : (
                    <View key={index} style={{ marginVertical: 5 }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                        <Text style={{ width: 100, fontWeight: 'bold', color: 'black' }}>Date :</Text>
                        <Text style={styles.dateFont}>{date}</Text>
                      </View>
                      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                        <Text style={{ width: 100, fontWeight: 'bold', color: 'black' }}>Time :</Text>
                        <Text style={styles.dateFont}>{bookingTime.startTime} - {bookingTime.endTime}</Text>
                        <View style={{ width: "90%" , marginLeft:"1%" }}><Text style={{ color: "#000", fontSize: 14, lineHeight: 20 }}>
                             <Text style={{ color: bookingTime?.status === "upcoming" ? "green" : "red" }}>({bookingTime?.status})</Text>
                          </Text></View>
                      </View>
                    </View>
                  )
                );
              })}
            </ScrollView>
            <View style={styles.cancleButton}>

              <TouchableOpacity onPress={toggleDatesModal} style={{ marginRight: "2%" }}>
                <View style={{
                  width: "100%",
                  height: 35,
                  borderRadius: 3,
                  backgroundColor: "#000",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  padding: "5%"
                }}>
                  <Text style={{ color: "#fff", }}>Close</Text>
                </View>

              </TouchableOpacity>
            </View>
          </View>
        </View>

      </Modal>
      <Footer />
    </ScrollView>
  );
};

export default BookingDetails;

const styles = StyleSheet.create({
  container: {
    padding: 20,
    // backgroundColor: 'skyblue',
  },
  card: {
    backgroundColor: '#FAFBFCFF',
    borderRadius: 10,
    marginBottom: 20,
    padding: 15,
    elevation: 1,
    marginHorizontal: '4%',
    marginVertical: "4%"
  },
  bookingImage: {
    width: sliderWidth,
    height: width,
    resizeMode: 'cover',
    borderRadius: 12,
    alignSelf: 'center',
  },
  nameStatus: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: "5%"
  },
  status: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-around"
  },
  row: { flexDirection: 'row', alignItems: 'center', marginBottom: 8, color: "#000" },
  textCon: { width: 100, color: "#000", fontFamily: 'Lato-Bold', },
  textStyle: { fontWeight: 'bold', color: "#000" },
  needToCarry: { width: "65%" },
  scrollView: {
    flexDirection: 'column',
    alignItems: 'center',
    marginVertical: "2%"
  },

  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: "5%",
    width: '90%',
    maxHeight: '80%',
    // height:"30%"
    // alignItems: 'center', 
  },
  cancleButton: {
    flexDirection: "row",
    justifyContent: "flex-end",
    width: "100%",
    marginTop: "10%",
    alignItems: "center",
  },
  dateFont: {
    fontFamily: 'Lato-Regular', color: 'black'
  },
  valueWith: { color: "#000", width: "70%", fontFamily: 'Lato-Regular', }

});
