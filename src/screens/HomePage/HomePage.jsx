import {
  <PERSON><PERSON>rea<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>View,
  View,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import Header from '../../components/header.jsx';
import TopCategories from '../../components/homepage/TopCategories.jsx';
import TopCourses from '../../components/homepage/TopCourses.jsx';
import RecommendedCoaches from '../../components/homepage/RecommendedCoaches.jsx';
import WhyPeopleLoveKhelSports from '../../components/homepage/WhyPeopleLoveKhelSports.jsx';
import { useAuth } from '../../Context/AuthContext.jsx';
import Footer from '../../components/footer.jsx';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import RegisterAsCoach from '../RegisterAsCoach.jsx';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SearchLocationContainer from '../../components/SearchLocationContainer/SearchLocationContainer.tsx';
import { styles } from './homePageStyling.js';
import TopAcademies from '../../components/TopAcademies/TopAcademies.jsx';

const HomePage = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { playerData } = useAuth();

  const [CMSData, setCMSData] = useState([]);
  const [componentsToRender, setComponentsToRender] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Removed legacy AsyncStorage session restore logic

  useEffect(() => {
    const requestOptions = {
      method: 'GET',
      redirect: 'follow',
    };

    fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/blocks`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        setCMSData(result);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error(error);
      });
  }, []);

  useEffect(() => {
    if (CMSData.length > 0) {
      const sortedCMSData = CMSData.sort(
        (a, b) => a.blockData.position - b.blockData.position
      );

      // DRY: Map block identity to component
      const CMS_COMPONENT_MAP = {
        Category: TopCategories,
        Course: TopCourses,
        Testimonials: WhyPeopleLoveKhelSports,
        Registration: RegisterAsCoach,
        Coach: RecommendedCoaches,
        Academy: TopAcademies,
      };

      const components = sortedCMSData.map((data, index) => {
        const Component = CMS_COMPONENT_MAP[data.blockData.identity];
        if (!Component) return null;
        if (data.blockData.identity === 'Academy') {
          return (
            <Component
              key={`${data.id}-${index}`}
              academies={data.referencedData}
              loading={isLoading}
              blockData={data.blockData}
            />
          );
        }
        return (
          <Component
            key={`${data.id}-${index}`}
            data={data.referencedData}
            blockData={data.blockData}
          />
        );
      });
      setComponentsToRender(components);
    }
  }, [CMSData]);

  const handleDrawerOpen = (isOpen) => {
    setIsDrawerOpen(isOpen);
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Header onDrawerOpen={handleDrawerOpen} />
      <ScrollView scrollEnabled={!isDrawerOpen}>
        {/* <Search/> */}
        <SearchLocationContainer />
        <View style={styles.homeBackground}>
          {componentsToRender}
          <Footer />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomePage;


