import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
  Pressable,
  ScrollView,
  Dimensions,
} from 'react-native';
import { replace, useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../Context/AuthContext';
import { NEXT_PUBLIC_BASE_URL, androidClientId, webClientId } from '@env';
import Search from '../components/Search.jsx';
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';
GoogleSignin.configure({
  webClientId: webClientId,
  androidClientId: androidClientId,
  // iosClientId: GOOGLE_IOS_CLIENT_ID,
  scopes: ['profile', 'email'],
});
import axios from 'axios';
const GoogleLogin = async () => {
  await GoogleSignin.hasPlayServices();
  const userInfo = await GoogleSignin.signIn();
  return userInfo;
};

const SignIn = () => {
  const [showPassword, setShowPassword] = useState(false);
  const navigation = useNavigation();
  const { login, isLoggedIn, playerData } = useAuth();
  const [isSigningIn, setIsSigningIn] = useState(false);

  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  // useEffect(() => {
  //   const checkLoggedIn = async () => {
  //     const loggedInValue = await AsyncStorage.getItem('userLoggedIn');
  //     if (loggedInValue === 'true') {
  //       const token = await AsyncStorage.getItem('playerToken');
  //       const userId = await AsyncStorage.getItem('userIdNEw');
  //       setUser(userId, token);

  //     }
  //   };

  //   checkLoggedIn();
  // }, [playerData]);
  const handleGoogleLogin = async () => {
    if(loading) return;
    setLoading(true);
    try {
      const response = await GoogleLogin();
      const { idToken, user } = response;

      if (idToken && user) {
        const myHeaders = new Headers();
        myHeaders.append('Content-Type', 'application/json');

        const raw = JSON.stringify({
          email: user.email,
          firstName: user.givenName,
          lastName: user.familyName,
        });

        const requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: raw,
          redirect: 'follow',
        };
        const response = await fetch(
          `${NEXT_PUBLIC_BASE_URL}/api/player/google`,
          requestOptions,
        );
        const result = await response.json();
        const token = result?.token;
        const id = result?.id;

        if (result?.status === 'ok') {
          await login(id, token);
          navigation.navigate('Home');
        } else {
          Alert.alert('Failed', result.error);
        }
      }
    } catch (error) {
      setError(
        error?.response?.data?.error?.message ||
        'Something went wrong during Google login.',
      );
    } finally {
      setLoading(false);
    }
  };

  const signInSchema = Yup.object().shape({
    email: Yup.string()
      .email('Invalid email address')
      .required('Please enter your email'),
    password: Yup.string()
    .matches(/^(?=\S+$).*/, 'Password cannot contain spaces')
      .min(8, 'Password must be at least 8 characters')
      .required('Please enter your password'),
  });
  const formik = useFormik({
    initialValues: { email: '', password: '' },
    validationSchema: signInSchema,
    onSubmit: async values => {
      const { email, password } = values;
      try {
        const response = await fetch(
          `${NEXT_PUBLIC_BASE_URL}/api/player/login`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
          },
        );
        const data = await response.json();
        if (response.ok) {
          await login(data.id, data.token);
          navigation.navigate('Home');
        } else if (data && data.error) {
          Alert.alert('Error', data.error);
        } else {
          Alert.alert('Error', data.message);
        }
      } catch (error) {
        console.error('Login error:', error);
        Alert.alert(
          'Error',
          'An error occurred during login. Please try again.',
        );
      }
    },
  });
  const toggleShowPassword = () => setShowPassword(!showPassword);

  const screenWidth = Dimensions.get('window').width;

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
        <ScrollView> 
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../../src/assets/MainKhelCoach.png')}
              style={styles.headerLogo}
            />
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.title}>Sign in to your account</Text>
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.labelEmail}>Email Address</Text>
            <TextInput
              style={[
                styles.input,
                formik.touched.email && formik.errors.email
                  ? styles.errorBorder
                  : null,
              ]}
              placeholder="Enter Email Address"
              onChangeText={text => formik.setFieldValue('email', text.toLowerCase())}
              onBlur={formik.handleBlur('email')}
              value={formik.values.email}
              keyboardType="email-address"
              placeholderTextColor="#9CA3AF"
            />
            <Text style={styles.errorText}>
              {formik.touched.email && formik.errors.email}
            </Text>

            <View style={styles.passwordRow}>
              <Text style={styles.label}>Password</Text>
              {/* <TouchableOpacity
                onPress={() => navigation.navigate('ForgotPassword')}>
                <Text style={styles.forgotPassword}>Forgot Password?</Text>
              </TouchableOpacity> */}
            </View>
            <View style={{ position: 'relative' }}>
              <TextInput
                style={[
                  styles.input,
                  formik.touched.password && formik.errors.password
                    ? styles.errorBorder
                    : null,
                ]}
                placeholder="Enter Password"
                onChangeText={formik.handleChange('password')}
                onBlur={formik.handleBlur('password')}
                value={formik.values.password}
                secureTextEntry={!showPassword}
                placeholderTextColor="#9CA3AF"
              />
              <TouchableOpacity
                onPress={toggleShowPassword}
                style={{ position: 'absolute', top: 13, right: 10 }}>
                <Image
                  source={
                    showPassword
                      ? require('../../src/assets/eye.png')
                      : require('../../src/assets/eye-crossed.png')
                  }
                  style={styles.eyeIcon}
                />
              </TouchableOpacity>
            </View>
            <Text style={styles.errorText}>
              {formik.touched.password && formik.errors.password}
            </Text>
            <View style={styles.actionRow}>
              <TouchableOpacity
                style={styles.leftActionContainer}
                onPress={() => navigation.navigate('ForgetPassword')}>
                <Text style={styles.forgotPassword}>Forgot Password?</Text>
              </TouchableOpacity>
              <View style={styles.rightActionContainer}>
                <Text style={styles.notamem}>
                  <Text style={{ color: '#000' }}> Not a member?</Text>
                  <TouchableOpacity onPress={() => navigation.navigate('SignUp')}>
                    <Text style={styles.signUpText}> Sign Up</Text>
                  </TouchableOpacity>
                </Text>
              </View>
            </View>

            <View>
              <TouchableOpacity
                style={styles.signInButton}
                onPress={formik.handleSubmit}>
                <Text style={styles.signInButtonText}>Sign In</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.orContinueContainer}>
              <View style={styles.orLine}></View>
              <Text style={styles.orText}>Or continue with</Text>
              <View style={styles.orLine}></View>
            </View>
  
              <TouchableOpacity
                activeOpacity={0.8}
                style={{ width: '100%', alignItems: 'center', marginTop: 20 }}
                onPress={handleGoogleLogin}
              >
                <GoogleSigninButton
                  style={{ width: '100%', height: 48, flex: 1 }}
                  size={GoogleSigninButton.Size.Wide}
                  color={GoogleSigninButton.Color.Dark}
                />
              </TouchableOpacity>
            
          </View>
        </View>
      </TouchableWithoutFeedback></ScrollView>
     
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: '5%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
    margin: '3%',
    padding: '5%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 1,
  },
  logoContainer: {
    marginBottom: '5%',
  },
  headerLogo: {
    width: 200,
    height: 100,
    resizeMode: 'contain',
  },
  textContainer: {
    marginBottom: '5%',
    color: '#000',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: '7%',
    color: '#000',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    marginBottom: '1%',
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    width: '100%',
    color: '#000',
  },
  labelEmail: {
    fontSize: 14,
    marginBottom: '2%',
    color: '#000',
  },
  label: {
    fontSize: 14,
    color: '#000',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    color: '#0EA5E9',
    marginBottom: '2%',
    fontWeight: '600',
  },
  passwordRow: {
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: '2%', // Adjust spacing as needed
  },
  eyeIcon: {
    width: 24,
    height: 24,
  },
  actionRow: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: '2%',
  },
  leftActionContainer: {
    flexShrink: 1,
    flexGrow: 0,
    maxWidth: '50%',
  },
  rightActionContainer: {
    flexGrow: 1,
    flexShrink: 1,
    flexBasis: 0,
    alignItems: 'flex-end',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    minWidth: 0,
  },
  signInButton: {
    // marginTop: '2%',
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    height: 50,
  },
  signInButtonText: {
    color: '#FAFBFCff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
  orContinueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: '7%',
    marginBottom: '1%',
  },
  orLine: {
    flex: 1,
    height: 1,
    opacity: 0.5,
    backgroundColor: 'grey',
    marginLeft: 5,
    marginRight: 5,
  },
  orText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: 'grey',
  },
  googleSignInButton: {
    flexDirection: 'row',
    backgroundColor: '#FAFBFCFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  googleSignInButtonText: {
    marginLeft: 10,
    color: '#000',
    fontSize: 16,
  },
  googleLogo: {
    width: 24,
    height: 24,
  },
  notamem: {
    fontSize: 14,
    color: 'grey',
    flexWrap: 'wrap',
  },
  signUpText: {
    color: '#0EA5E9',
    fontWeight: '600',
  },
  signUpLink: {
    textDecorationLine: 'underline',
  },
  errorText: {
    color: 'red',
    marginBottom: 5,
  },
  errorBorder: {
    borderColor: 'red',
  },
  signInCon: {
    marginTop: 20,
    alignItems: "center",
  },
});

export default SignIn;
