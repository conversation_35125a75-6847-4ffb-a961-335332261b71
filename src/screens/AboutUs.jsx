import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, ActivityIndicator, Dimensions, Image } from 'react-native';
import RenderHtml from 'react-native-render-html';
import HTML from 'react-native-render-html';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import axios from 'axios';

const AboutUs = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [aboutUS, setAboutUS] = useState(null);
  const [otherDetail, setOtherDetail] = useState([]);
  const [imageUrls, setImageUrls] = useState({});

  const fetchData = async () => {
    try {
      const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/api/cms/cms-about-us`);
      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }
      const data = await response.json();
      setAboutUS(data[0]);
      setOtherDetail(data[0]?.founderDetails || []);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const { width } = Dimensions.get('window');

  const extractPreviewUrl = async (url) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_BASE_URL}/api/coach/download`,
        { location: url },
        { headers: { "Content-Type": "application/json" } }
      );
      return response.data.url;
    } catch (error) {
      console.error("Error fetching preview URL:", error);
      return null;
    }
  };

  useEffect(() => {
    const fetchImageUrls = async () => {
      const urls = {};
      for (const detail of otherDetail) {
        if (detail.image) {
          const resolvedUrl = await extractPreviewUrl(detail.image);
          urls[detail.image] = resolvedUrl;
        }
      }
      setImageUrls(urls);
    };

    if (otherDetail && otherDetail.length > 0) {
      fetchImageUrls();
    }
  }, [otherDetail]);

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>About Us</Text>
      {aboutUS && (
        <View style={styles.policyContainer}>
          <View style={styles.policyContainer}>
            <HTML tagsStyles={{
              p: { margin: 0, padding: 0, color: "#000", fontFamily: "Lato-Regular" },
              h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
              br: { display: 'none' },
              li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
              ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
            }} source={{ html: aboutUS?.aboutUsData }} />
          </View>
        </View>
      )}
      {aboutUS && (
        <View style={styles.policyContainer}>
          <View style={styles.policyContainer}>
            <HTML tagsStyles={{
              p: { margin: 0, padding: 0, color: "#000", fontFamily: "Lato-Regular" },
              h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
              br: { display: 'none' },
              li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center' },
              ul: { marginHorizontal: "5%", alignItems: 'center', padding: 0 },
            }} source={{ html: aboutUS?.founderDetails }} />
          </View>
        </View>
      )}
      {otherDetail.map((x, index) => (
        <View
          key={x.id}
          style={{
            flexDirection: index % 2 === 0 ? "row" : "row-reverse", // Alternating row directions
            marginBottom: 16,
            alignItems: "center",
          }}
        >
          <View style={{ flex: 1, padding: 8 }}>
            {imageUrls[x.image] ? (
              <Image
                source={{ uri: imageUrls[x.image] }}
                style={{
                  width: "100%",
                  height: 200,
                  resizeMode: "cover",
                  borderRadius: 8,
                }}
              />
            ) : (
              <ActivityIndicator size="small" color="#0000ff" />
            )}
          </View>

          <View style={{ flex: 1, padding: 8 }}>
            <HTML
              tagsStyles={{
                p: { margin: 0, padding: 0, color: "#000", fontFamily: "Lato-Regular" },
                h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
                br: { display: "none" },
                li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: "center" },
                ul: { marginHorizontal: "5%", alignItems: "center", padding: 0 },
              }}
              source={{ html: x.description }}
            />
          </View>
        </View>
      ))}

    </ScrollView>
  );
};

export default AboutUs;

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    paddingVertical: "2%",
    paddingHorizontal: "10%",
    alignContent: "center"
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000',
  },
  policyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bulletPoint: {
    marginTop: 5,
    fontSize: 16,
  },
});
