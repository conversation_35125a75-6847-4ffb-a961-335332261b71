import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { styles } from './styles';


interface CourseData {
  _id: string;
  courseName: string;
  classType: string;
  fees: {
    fees: number;
    fees30: number;
    fees45: number | null;
    fees60: number | null;
    feesCourse: number | null;
  };
  images: Array<{ url?: string; [key: string]: any }>;
}

interface SearchCourseCardProps {
  course: CourseData;
  onPress?: (course: CourseData) => void;
}

const SearchCourseCard: React.FC<SearchCourseCardProps> = ({ course, onPress }) => {
  // console.log("Course Inside Card", course.images[0].url);
  // Calculate the fee to display based on classType
  const getDisplayFee = () => {
    if (course.classType === "course") {
      return course.fees.feesCourse || course.fees.fees;
    } else {
      // For classes, show the lowest fee between fees30 and fees60
      const fees30 = course.fees.fees30;
      const fees60 = course.fees.fees60;
      
      if (fees30 && fees60) {
        return Math.min(fees30, fees60);
      } else if (fees30) {
        return fees30;
      } else if (fees60) {
        return fees60;
      } else {
        return course.fees.fees;
      }
    }
  };

  // Get the first image URL
  const getImageUrl = (): string | null => {
    if (course.images && course.images.length > 0) {
      console.log("If condition triggered------------------------>", course.images);
      const firstImage = course.images[0];
      if (typeof firstImage === 'string') {
        console.log("First If condition----------------------", firstImage)
        return firstImage;
      } else if (firstImage && typeof firstImage === 'object' && firstImage.url) {
        console.log("Second If condition--------------------", firstImage)
        return firstImage.url;
      }
    }
    return null;
  };

  const displayFee = getDisplayFee();
  const imageUrl = getImageUrl();
  console.log("Image url-----------------", imageUrl);
  return (
    <TouchableOpacity
      style={styles.cardContainer}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
    >
      {/* Left Container - Image */}
      <View style={styles.leftContainer}>
        {imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            style={styles.courseImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={styles.placeholderText}>No Image</Text>
          </View>
        )}
      </View>

      {/* Right Container - Content */}
      <View style={styles.rightContainer}>
        <View style={styles.topContent}>
          <Text style={styles.courseName} numberOfLines={2}>
            {course.courseName}
          </Text>
        </View>

        <View style={styles.bottomContent}>
          <Text style={styles.startingFromText}>Starting from</Text>
          <Text style={styles.feeAmount}>₹{displayFee}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SearchCourseCard;