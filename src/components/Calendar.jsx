import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';

const timeToMinutes = (time) => {
  if (!time || !time.includes(':')) {
    console.error('Invalid or missing time value:', time);
    return 0; // Returning 0 to avoid further errors, adjust based on your error handling policy
  }
  const [hours, minutes] = time.split(':').map(Number);
  if (isNaN(hours) || isNaN(minutes)) {
    console.error('Failed to parse time:', time);
    return 0;
  }
  return hours * 60 + minutes;
};

const formatTimeWithAmPm = (time) => {
  const [hours, minutes] = time.split(':');
  const hoursInt = parseInt(hours, 10);
  const suffix = hoursInt >= 12 ? 'PM' : 'AM';
  const formattedHours = ((hoursInt + 11) % 12 + 1);
  return `${formattedHours}:${minutes} ${suffix}`;
};

const CalendarSlot = ({ slot, minuteHeight }) => {
  const startMinutes = timeToMinutes(slot.start);
  const endMinutes = timeToMinutes(slot.end);
  const duration = endMinutes - startMinutes;
  const slotHeight = Math.max(duration * minuteHeight, 30);
  const slotTopPosition = startMinutes * minuteHeight;
  return (
    // <View style={styles.slot}>
    //   <Text style={styles.slotText}>{`${formatTimeWithAmPm(slot.start)} - ${formatTimeWithAmPm(slot.end)}`}</Text>
    //   <Text style={styles.slotTitle}>{slot.title}</Text>
    // </View>
    <View style={[styles.slot, { top: slotTopPosition, height: slotHeight }]}>
      <Text style={styles.slotText}>{`${formatTimeWithAmPm(slot.start)} - ${formatTimeWithAmPm(slot.end)}`}</Text>
      <Text style={styles.slotTitle}>Booked</Text>
    </View>
  );
};


const TimeAxis = ({ minuteHeight }) => {
  const times = [];
  for (let i = 0; i <= 23; i++) {
    times.push(
      <View key={i} style={{ height: 60 * minuteHeight }}>
        <Text style={styles.timeText}>{formatTimeWithAmPm(`${i}:00`)}</Text>
      </View>
    );
  }
  return <View style={styles.timeAxis}>{times}</View>;
};

const CustomCalendar = ({ bookedSlots }) => {
  const minuteHeight = 2;
  const calendarHeight = 24 * 60 * minuteHeight;
  return (
    <ScrollView style={[styles.container]} contentContainerStyle={{ height: calendarHeight }} showsVerticalScrollIndicator={true}>
      {/* Render horizontal lines after each hour */}
      {[...Array(24)].map((_, i) => (
        <View
          key={`line-${i}`}
          style={[
            styles.fullHorizontalLine,
            { top: i * 60 * minuteHeight, position: 'absolute' },
          ]}
        />
      ))}
      <TimeAxis minuteHeight={minuteHeight} />
      {bookedSlots.map((event, index) => (
        <CalendarSlot key={index} slot={event} minuteHeight={minuteHeight} />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8FF',
    position: 'relative',
    width: '100%',
    borderRadius: 10,
    marginBottom: 10,
    // elevation: 1,
  },
  slot: {
    position: 'absolute',
    left: 55,
    right: 10,
    backgroundColor: 'lightgrey',
    borderRadius: 5,
    padding: 5,
  },
  slotText: {
    color: 'grey',
    fontWeight: 'bold',
  },
  slotTitle: {
    color: '#000',
    fontWeight:"500"
  },
  timeAxis: {
    position: 'absolute',
    left: 5,
    top: 0,
    bottom: 0,
    width: 50,
    paddingRight: 5,
    borderRightWidth: 1,
    borderColor: '#ddd',
  },
  timeText: {
    color: '#000',
    fontSize: 14,
  },
  fullHorizontalLine: {
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#ddd',
    zIndex: 0,
  },
});

export default CustomCalendar;
