import React, { useEffect, useState } from "react";
import { Modal, View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native";
import { NEXT_PUBLIC_BASE_URL, RAZORPAY_KEY, NEXT_WALLET_URL } from '@env';
import HTML from 'react-native-render-html';

import axios from "axios";

const TermsAndConditionModal = ({ open, setOpen, saveData, playerData }) => {
  const [policy, setPolicy] = useState("");
  const [loading, setLoading] = useState(false);

  const getPolicy = async () => {
    setLoading(true);
    try {
      const data = await axios.get(
        `${NEXT_PUBLIC_BASE_URL}/api/cms/cms-player-termsAndCondition-details`
      );
      setPolicy(data?.data[0]?.termsAndConditionData);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getPolicy();
  }, [open, saveData]);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={open}
      onRequestClose={() => setOpen(!open)}
    >
      <TouchableOpacity 
        style={styles.modalContainer} 
        activeOpacity={1} 
        onPress={() => setOpen(!open)}
      >
        <TouchableOpacity 
          style={styles.modalContent} 
          activeOpacity={1}
          onPress={(e) => e.stopPropagation()}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Terms of Service</Text>
          </View>

          <ScrollView style={styles.content}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Loading terms and conditions...</Text>
              </View>
            ) : (
              <HTML
                tagsStyles={{
                  p: { margin: 0, paddingHorizontal: "2%", color: "#000", fontFamily:"Lato-Regular" }, 
                  li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center'},
                  ol: { marginHorizontal: "5%" , alignItems: 'center', padding:0, listStyleType: "none"},
                  ul: { marginHorizontal: "5%" , alignItems: 'center', padding:0, listStyleType: "none"}, 
                  h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
                }} 
                source={{ html: policy }} 
              />
            )}
          </ScrollView>

          <View style={styles.footer}>
            {!loading && (
              <TouchableOpacity
                style={styles.acceptButton}
                onPress={() => saveData.handleSubmit()}
              >
                <Text style={styles.buttonText}>I Accept</Text>
              </TouchableOpacity>
            )}

            {/* Uncomment below for Decline button if necessary */}
            {/* <TouchableOpacity
              style={styles.declineButton}
              onPress={() => setOpen(!open)}
            >
              <Text style={styles.buttonText}>Decline</Text>
            </TouchableOpacity> */}
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "90%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    elevation: 10,
  },
  header: {
    borderBottomWidth: 1,
    borderColor: "#ddd",
    paddingBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
  },
  content: {
    marginVertical: 20,
    maxHeight: 300,
  },
  policyText: {
    fontSize: 14,
    color: "#666",
  },
  footer: {
    borderTopWidth: 1,
    borderColor: "#ddd",
    paddingTop: 10,
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  acceptButton: {
    backgroundColor: "#1E90FF",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  buttonText: {
    color: "white",
    fontWeight: "bold",
    textAlign: "center",
  },
  declineButton: {
    backgroundColor: "#FF6347",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginLeft: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default TermsAndConditionModal;
