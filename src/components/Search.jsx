import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, TextInput, TouchableOpacity, ScrollView, Text, Image, PermissionsAndroid } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import debounce from 'lodash/debounce';
import { NEXT_PUBLIC_BASE_URL, GOOGLE_PLACES_API_KEY } from '@env';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Geolocation from '@react-native-community/geolocation';
const searchIcon = require('../assets/search.png');
const searchIconTwo = require('../assets/searchicon.png');
const location = require('../assets/locationsearch.png')

const Search = () => {
  const [searchQueryValue, setsearchQueryValue] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const navigation = useNavigation();
  const [uniqueCoaches, setUniqueCoaches] = useState([]);
  const [error, setError] = useState(false);
  const [shouldShowListView, setShouldShowListView] = useState(true);
  const [cityName, setCityName] = useState('');
const ref = useRef();
  useEffect(() => {
    requestLocationPermission();
  }, []);
  const requestLocationPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          { 
            title: "Location Access Required",
            message: "This app needs to access your location",
            buttonPositive: "OK",
            buttonNegative: "Cancel"
          }
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          getCurrentLocation();
        } else {
          Alert.alert('Location permission denied');
        }
      } catch (err) {
        console.warn(err);
      }
    } else {
      getCurrentLocation();
    }
  }
  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition((info) => {
    const { latitude, longitude } = info.coords;
    setSearchLocationObj({
      lat: latitude,
      lan: longitude
    });
    getCityNameFromCoordinates(latitude, longitude);

  });
  };
  const [searchLocationObj, setSearchLocationObj] = useState({
    lat: null,
    lan: null
  });
  const handleSelectLocation = (data, details) => {
    if (details && details.geometry && details.geometry.location) {
      const { lat, lng } = details.geometry.location;
      setSearchLocationObj({
        lat: lat,
        lan: lng
      });
      setShouldShowListView(false);
    }
  };
  const getCityNameFromCoordinates = async (latitude, longitude) => {
    const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${GOOGLE_PLACES_API_KEY}`;
    try {
      const response = await fetch(apiUrl);
      const data = await response.json();
      if (data.results && data.results.length > 0) {
        const addressComponents = data.results[0].formatted_address;

        if (addressComponents) {
          setCityName(addressComponents);
          ref.current?.setAddressText(addressComponents);
        }
      }
    } catch (error) {
      console.error('Error fetching city name:', error);
    }
  };
  const fetchSuggestions = async (query) => {
    if (query.length < 3) {
      setSuggestions([]);
      setUniqueCoaches([]);
      return;
    }

    const apiUrl = (searchLocationObj.lat && searchLocationObj.lan) ?
      `${NEXT_PUBLIC_BASE_URL}/api/course/filter?lat=${searchLocationObj.lat}&long=${searchLocationObj.lan}&q=${query}` :
      `${NEXT_PUBLIC_BASE_URL}/api/course/filter?q=${query}`;
    try {
      const response = await fetch(apiUrl);
      const data = await response.json();
      if (Array.isArray(data)) {
        setSuggestions(data);
        // Filter unique coaches
        const coachesById = data.reduce((acc, curr) => {
          acc[curr.coach_id] = curr;
          return acc;
        }, {});
        setUniqueCoaches(Object.values(coachesById));
      } else {
        console.error('Data is not an array:', data);
      }
    } catch (error) {
      console.error('Fetching suggestions error: 58');
    }
  };
  const debouncedFetchSuggestions = debounce(fetchSuggestions, 300);
  useEffect(() => {
    debouncedFetchSuggestions(searchQueryValue);
    return () => debouncedFetchSuggestions.cancel();
  }, [searchQueryValue]);

  const handleSearch = () => {
    // if (searchQueryValue.length < 3) {
    //   setError(true);
    //   return;
    // }
    let searchQuery = searchLocationObj.lat && searchLocationObj.lan ? `&q=${searchQueryValue}&lat=${searchLocationObj.lat}&long=${searchLocationObj.lan}` : `&q=${searchQueryValue}`
    navigation.navigate('Collection', { searchQuery, searchQueryValue });
    setSuggestions([]);
    setUniqueCoaches([]);
    setsearchQueryValue('')
  };
  const handleCategorySelect = (item) => {
    navigation.navigate('Courses', { courseId: item._id });
    setSuggestions([]);
    setUniqueCoaches([]);
    setsearchQueryValue('')
  };
  const handleCoachSelect = (coachId) => {
    navigation.navigate('CoachProfile', { ID: coachId });
    setSuggestions([]);
    setUniqueCoaches([]);
    setsearchQueryValue('')
  };
  return (
    <View style={styles.container}>
      <GooglePlacesAutocomplete
        placeholder={ "City Name"}

        placeholderTextColor="#000"
    ref= {ref}
        onPress={handleSelectLocation}
        fetchDetails={true}
        query={{
          key: GOOGLE_PLACES_API_KEY,
          language: 'en',
        }}
        listViewDisplayed={shouldShowListView}
        textInputProps={{
          onChangeText: (text) => {
            if (text === "") {
              setSearchLocationObj({
                lat: null,
                lan: null,
              });
            }
          },
        }}

        debounce={200}
        styles={{
          textInputContainer: styles.locationInputContainer,
          textInput: styles.locationTextInput,
          listView: {
            backgroundColor: '#fff',
          },
          description: {
            color: '#000',
          },
        }}
      />
      <View style={{ position: "relative ", marginTop:"4%" }}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.input}
            value={searchQueryValue}
            onChangeText={(text) => {
              setsearchQueryValue(text);
  // Clear error as user types
            }}
            placeholder="Course/Coach/Sports"
            placeholderTextColor="#000"
          />
          {/* <TouchableOpacity onPress={handleSearch} style={styles.iconContainer}> */}
          {/* <Image source={searchIcon} style={styles.icon} />
        </TouchableOpacity> */}
        </View>
        {error && <Text style={styles.errorText}>Please enter at least 3 characters.</Text>}
        <TouchableOpacity onPress={handleSearch}>
          <View style={styles.searchButton}>
            <Image source={searchIconTwo} style={styles.icon} />
            <Text style={{ color: "#fff", fontSize: 13, marginLeft: 5, fontFamily: 'Lato-Regular' }}>Search</Text>
          </View>
        </TouchableOpacity>
        {suggestions.length > 0 && (
          <View style={styles.suggestionsOverlay}>
            <ScrollView nestedScrollEnabled={true} style={styles.suggestionsScroll} contentContainerStyle={styles.scrollView}>
              <Text style={styles.categoryHeading}>Courses</Text>
              {suggestions && suggestions.filter((x)=>x.course !== "false").map((item) => (
                <TouchableOpacity key={item._id} onPress={() => handleCategorySelect(item)} style={styles.suggestionItem}>
                  <Text style={{ color: '#000' }}>{item.courseName}</Text>
                </TouchableOpacity>
              ))}
              <Text style={styles.categoryHeading}>Coaches</Text>
              {uniqueCoaches.map((coach) => (
                <TouchableOpacity key={coach.coach_id} onPress={() => handleCoachSelect(coach.coach_id)} style={styles.suggestionItem}>
                  <Text style={styles.coachName}>{coach.coachName}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )
          // :<View style={styles.suggestionsOverlay}><Text style={styles.categoryHeading}>Courses</Text></View>
        }
      </View>
    </View>
  );
};

export default Search;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: "5%",
    paddingVertical: "5%",
    // marginBottom: "1%",
    backgroundColor: "#0EA5E9",
    zIndex: 2

  },
  searchContainer: {
    flexDirection: 'row',
    borderColor: '#ccc',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderRadius: 5,
    // marginTop: 10,
    color: '#000',

  },
  locationInputContainer: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    borderTopWidth: 0,
    borderRadius: 5,
  },

  locationTextInput: {
    marginLeft: 0,
    marginRight: 0,
    height: 48,
    color: '#000',
    borderColor: '#ccc',
    fontSize: 16,
    borderRadius: 5,
    borderWidth: 1,
    padding: 10,
    fontFamily: 'Lato-Regular'

  },
  input: {
    flex: 1,
    padding: 10,
    color: '#000',
    fontSize: 16,
    fontFamily: 'Lato-Regular'
  },
  iconContainer: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: 20,
    height: 20,
  },
  suggestionsOverlay: {
    position: 'absolute',
    top: 108,
    left: 0,
    right: 20,
    backgroundColor: '#fff',
    maxHeight: 500,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 5,
    zIndex: 99999999999,
    color: "#000",
    width: "100%"
  },
  suggestionsScroll: {
    paddingHorizontal: 10,

  },
  scrollView: {
    flexDirection: 'column',
    marginVertical: "2%"
  },
  suggestionItem: {
    padding: 10,
    borderColor: '#ddd',
    borderWidth: 1,
    marginTop: 5,
    backgroundColor: '#fff',
    color: "#000"
  },
  categoryHeading: {
    fontWeight: 'bold',
    marginTop: 10,
    color: '#000'
  },
  coachName: {
    marginLeft: 10,
    color: '#666',
  },
  locationContainer: {
    flexDirection: 'row',
    color: "#000",
    backgroundColor: 'transparent',
    marginBottom: "4%"
  },
  searchButton: {
    backgroundColor: "#000",
    color: "white",
    display: "flex",
    flexDirection: "row",
    borderRadius: 5,
    padding: 12,
    justifyContent: "center",
    marginTop: "3%",
    alignItems: "center"
  },
  errorText: {
    color: '#fff',
    marginTop: 5,
    fontFamily: "Lato-Bold"
  },
});
