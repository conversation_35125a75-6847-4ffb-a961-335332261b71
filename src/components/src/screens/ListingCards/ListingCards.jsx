import React, { useState } from "react";
import {
  View,
  Text,
  Linking,
  FlatList,
  SafeAreaView,
  TextInput
} from "react-native";

import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../../Context/AuthContext";
import { styles } from "./listingCardStyling";
import CourseCard from "./CourseCard";
import NoCourses from "./NoCourses";




export default function ListingCards({ courses, isLoading, hasMore, onEndReached, onRefresh, handleNotifyMe }) {
  const navigation = useNavigation();
  const { playerData } = useAuth();
  const [emailError, setEmailError] = useState("");
  const [notifySuccess, setNotifySuccess] = useState(false);
  const [email, setEmail] = useState(playerData?.email || "");

  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

  const onNotifyPress = () => {
    if (!emailRegExp.test(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    setEmailError("");
    handleNotifyMe(email);
    setNotifySuccess(true);
  };


  

  const handleMapNavigation = (latitude, longitude) => {
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    Linking.openURL(url);
  };

  return (
    <SafeAreaView>
      <Text style={styles.titleText}>
        {courses.length === 0 && !isLoading ? (
          <NoCourses
            notifySuccess={notifySuccess}
            email={email}
            setEmail={setEmail}
            emailError={emailError}
            onNotifyPress={onNotifyPress}
            styles={styles}
          />
        ) : (
          `No of Courses/Classes: ${courses.length}`
        )}
      </Text>
      <FlatList
        data={courses}
        renderItem={({ item, index }) => (
          <CourseCard
            course={item}
            index={index}
            navigation={navigation}
            handleMapNavigation={handleMapNavigation}
            styles={styles}
          />
        )}
        keyExtractor={(item) => item?._id}
        onEndReached={onEndReached}
        onEndReachedThreshold={0.5}
        refreshing={isLoading}
        onRefresh={onRefresh}
        ListFooterComponent={() => {
          if (!hasMore && courses.length > 0) {
            return (
              <View style={{ alignItems: 'center', marginBottom: 20 }}>
                <Text style={{ color: 'gray', fontSize: 16 }}>
                  You've reached the end of the available courses.
                </Text>
              </View>
            );
          }
          return <></>;
        }}
      />
    </SafeAreaView>
  );
}