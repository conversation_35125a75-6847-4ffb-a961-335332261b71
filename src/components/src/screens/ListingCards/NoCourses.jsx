import React from "react";
import { View, Text, TextInput, TouchableOpacity } from "react-native";

export default function NoCourses({ notifySuccess, email, setEmail, emailError, onNotifyPress, styles }) {
  return (
    <View style={{ width: '100%', ...styles.noCoursesContainer }}>
      <Text style={{ color: '#000', fontSize: 16, marginBottom: 8, textAlign: 'left' }}>
        No courses available at the moment.
      </Text>
      {notifySuccess ? (
        <Text style={{ color: '#0EA5E9', fontSize: 15, marginTop: 8, textAlign: 'left' }}>
          You will be notified when new courses are available.
        </Text>
      ) : (
        <View style={{ width: '100%' }}>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={text => setEmail(text.toLowerCase())}
            placeholder="Enter your email"
            placeholderTextColor="#888"
            keyboardType="email-address"
            autoCapitalize="none"
            color="#000"
          />
          {emailError ? (
            <Text style={{ color: 'gray', fontSize: 13, marginBottom: 4, textAlign: 'left' }}>{emailError}</Text>
          ) : null}
          <TouchableOpacity style={styles.notifyButton} onPress={onNotifyPress}>
            <Text style={{ color: '#fff', fontSize: 15, textAlign: 'center' }}>Notify Me</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
} 