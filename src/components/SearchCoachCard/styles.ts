import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    flexDirection: 'row',
    height: 120,
  },
  leftContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  rightContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'flex-start',
  },
  coachImage: {
    width: '100%',
    height: '100%',
    objectFit: 'fill',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    color: '#999',
    fontSize: 14,
    fontFamily: 'Lato-Regular',
  },
  topContent: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  coachName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    fontFamily: 'Lato-Bold',
    lineHeight: 20,
  },
});