import React, { useEffect, useState } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { styles } from './styles';
import axios from 'axios';

interface CoachData {
  _id: string;
  coachName: string;
  lastName?: string;
  email: string;
  image?: string;
  images?: string;
  sportsCategories: string[];
  status: string;
  coach_id: string;
}

interface SearchCoachCardProps {
  coach: CoachData;
  onPress?: (coach: CoachData) => void;
  imageUrl?: string | null;
}

const SearchCoachCard: React.FC<SearchCoachCardProps> = ({ coach, onPress, imageUrl }) => {
  const navigation = useNavigation();

  // Get the coach's full name
  const getFullName = () => {
    if (coach.lastName) {
      return `${coach.coachName} ${coach.lastName}`;
    }
    return coach.coachName;
  };

  // Get the coach's image URL, preferring the imageUrl prop if provided
  const getCoachImageUrl = (): string | null => {
    if (imageUrl) {
      // console.log("Image URL from Prop: ", imageUrl);
      return imageUrl;
    } // Early return if prop is provided
    let img = null;
    if ("image" in coach && coach.image) {
      img = coach.image;
    } else if ("images" in coach && coach.images && Array.isArray(coach.images) && coach.images[0]?.url) {
      img = coach.images[0].url;
    }
    return img;
  };

  const fullName = getFullName();
  const displayImageUrl = getCoachImageUrl();

  return (
    <TouchableOpacity
      style={styles.cardContainer}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {/* Left Container - Image */}
      <View style={styles.leftContainer}>
        {displayImageUrl ? (
          <Image
            source={{ uri: displayImageUrl }}
            style={styles.coachImage}
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Text style={styles.placeholderText}>No Image</Text>
          </View>
        )}
      </View>

      {/* Right Container - Content */}
      <View style={styles.rightContainer}>
        <View style={styles.topContent}>
          <Text style={styles.coachName} numberOfLines={2}>
            {fullName}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SearchCoachCard;