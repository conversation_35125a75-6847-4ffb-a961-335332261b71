import { StyleSheet } from 'react-native';
import { CARD_DIMENSIONS } from '../../constants/cardDimensions';

export default StyleSheet.create({
  courseItem: {
    width: CARD_DIMENSIONS.STANDARD_CARD_WIDTH,
    borderWidth: CARD_DIMENSIONS.CARD_BORDER_WIDTH,
    borderColor: CARD_DIMENSIONS.CARD_BORDER_COLOR,
    borderRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    backgroundColor: '#FAFBFC',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: CARD_DIMENSIONS.COURSE_CARD_IMAGE_HEIGHT,
  },
  courseDetails: {
    borderTopWidth: 1,
    borderColor: '#e2e2e2',
    paddingTop: 8,
    padding: 8,
  },
  priceContainer: {
    backgroundColor: '#E31F26',
    borderRadius: 4,
    marginBottom: 8,
    alignSelf: 'flex-start',
    paddingHorizontal: '5%',
    paddingVertical: '3%',
  },
  price: {
    color: '#FAFBFCff',
    fontWeight: 'bold',
  },
  courseName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#000',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  learnMore: {
    borderRadius: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: '0.5%',
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
}); 