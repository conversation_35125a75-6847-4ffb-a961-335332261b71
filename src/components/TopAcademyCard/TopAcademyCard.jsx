import { Text, View, TouchableOpacity, StyleSheet } from 'react-native';
import Image from '@d11/react-native-fast-image';
import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { CARD_DIMENSIONS } from '../../constants/cardDimensions';

const CARD_WIDTH = CARD_DIMENSIONS.STANDARD_CARD_WIDTH; // Use shared constant
const CARD_HEIGHT = 300; // Adjust to match TopCoach card


const redirect = require('../../assets/redirect.png');

const TopAcademyCard = ({ image, title, academyId, academyImages }) => {
  const navigation = useNavigation();
  const handleNavigation = ()=>{
    navigation.navigate("AcademyProfile", {id: academyId, academyImages, title, profileImg: image});
  }
  
  const renderImageContent = () => {
    if (image && image.uri) {
      return <Image source={image} style={styles.image} resizeMode="stretch" />;
    } else {
      return (
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>No Image</Text>
        </View>
      );
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={handleNavigation}>
      <View style={styles.imageContainer}>
        {renderImageContent()}
      </View>
      <View style={styles.body}>
        <Text style={styles.title} numberOfLines={1}>{title}</Text>
        <View style={styles.learnMore} >
          <Text style={styles.learnMoreText}>View Academy</Text>
          <Image source={redirect} style={{ width: 15, height: 15, marginLeft: 6 }} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    backgroundColor: '#fff',
    overflow: 'hidden',
    borderWidth: CARD_DIMENSIONS.CARD_BORDER_WIDTH,
    borderColor: CARD_DIMENSIONS.CARD_BORDER_COLOR,
  },
  imageContainer: {
    height: CARD_HEIGHT * 0.75,
    width: '100%',
    borderTopLeftRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    borderTopRightRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
  },
  image: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    borderTopRightRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    backgroundColor: "#d3d3d3",
    resizeMode: 'stretch',
  },
  placeholderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
    borderTopRightRadius: CARD_DIMENSIONS.CARD_BORDER_RADIUS,
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
    fontFamily: 'Lato-Regular',
  },
  body: {
    height: CARD_HEIGHT * 0.25,
    padding: 12,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    color: 'black',
    fontFamily: 'Lato-Bold',
    // No marginBottom to match TopCoach card
  },
  button: {
    backgroundColor: '#1e90ff',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
    marginTop: 'auto',
  },
  learnMore: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 4,
    marginTop: 'auto',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: 2,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },
});

export default TopAcademyCard;