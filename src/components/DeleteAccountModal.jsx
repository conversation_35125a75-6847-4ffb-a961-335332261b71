import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
} from 'react-native';
import { ScaledSheet as StyleSheet } from 'react-native-size-matters';
import { NEXT_PUBLIC_BASE_URL } from '@env';
import { useAuth } from '../Context/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';



const DeleteAccountModal = ({ visible, onClose, onSuccess }) => {
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { userToken, logout, userId } = useAuth();

  const handleDeleteAccount = async () => {
    if (!reason.trim()) {
      setError('Please enter your reason for deletion');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Direct external API call using axios (React Native pattern)
      const response = await axios.delete(`${NEXT_PUBLIC_BASE_URL}/api/player/${userId}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
        },
        data: {
          reason: reason.trim(),
        },
      });

      if (response.status === 200) {
        // Clear all storage after successful deletion
        await AsyncStorage.clear();
        logout();
        onSuccess();
        Alert.alert('Success', 'Your account has been deleted successfully.');
      } else {
        setError(response.data?.error || 'Failed to delete account. Please try again.');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else if (error.response?.status === 401) {
        setError('Unauthorized. Please login again.');
      } else if (error.response?.status === 403) {
        setError('Players who have booked sessions cannot delete their account.');
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setReason('');
    setError('');
    setLoading(false);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.title}>Confirm Account Deletion</Text>
            
            <Text style={styles.description}>
              Are you sure? Your profile and related account information will be deleted from our site.
            </Text>

            <Text style={styles.note}>
              Note: Players who have booked sessions cannot delete their account.
            </Text>

            <Text style={styles.prompt}>
              To confirm deletion, please enter your reason below:
            </Text>

            <TextInput
              style={[styles.textInput, error && styles.inputError]}
              placeholder="Please enter your reason"
              placeholderTextColor="#666666"
              value={reason}
              onChangeText={(text) => {
                setReason(text);
                if (error) setError('');
              }}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />

            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleCancel}
                disabled={loading}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.confirmButton, loading && styles.disabledButton]}
                onPress={handleDeleteAccount}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.confirmButtonText}>Confirm Deletion</Text>
                )}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: '10@ms',
    margin: '20@s',
    maxWidth: '400@s',
    maxHeight: '60%',
    minHeight: '400@vs',
  },
  scrollContent: {
    padding: '20@s',
    flexGrow: 1,
    gap: '12@vs',
  },
  title: {
    fontSize: '18@ms',
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#000000',
  },
  description: {
    fontSize: '14@ms',
    color: '#333333',
    lineHeight: '20@vs',
  },
  note: {
    fontSize: '13@ms',
    color: '#000000',
    fontWeight: 'bold',
    lineHeight: '18@vs',
  },
  prompt: {
    fontSize: '13@ms',
    color: '#FF8C00',
    fontWeight: 'bold',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: '8@ms',
    padding: '12@s',
    fontSize: '16@ms',
    minHeight: '80@vs',
    maxHeight: '120@vs',
    color: '#000000',
    backgroundColor: '#ffffff',
  },
  inputError: {
    borderColor: '#ff0000',
  },
  errorText: {
    color: '#FF0000',
    fontSize: '13@ms',
    fontWeight: '500',
    lineHeight: '18@vs',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: '10@s',
    marginTop: '8@vs',
  },
  button: {
    flex: 1,
    paddingVertical: '12@vs',
    paddingHorizontal: '20@s',
    borderRadius: '8@ms',
    alignItems: 'center',
    justifyContent: "center"
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  confirmButton: {
    backgroundColor: '#dc3545',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cancelButtonText: {
    color: '#333',
    fontSize: '16@ms',
    fontWeight: '500',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: '16@ms',
    fontWeight: '500',
  },
});

export default DeleteAccountModal; 