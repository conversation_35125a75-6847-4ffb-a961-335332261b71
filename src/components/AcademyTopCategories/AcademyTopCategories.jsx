import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ms, vs } from 'react-native-size-matters';

const AcademyTopCategories = ({ categories, title }) => {
  const navigation = useNavigation();

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{title || 'CATEGORIES'}</Text>
      <ScrollView 
        horizontal
        scrollEnabled={categories.length > 2} 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.scrollViewContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category._id}
            style={styles.category}
            activeOpacity={0.8}
            onPress={() =>
              navigation.navigate('Collection', { selectedOption: category.lowerCaseName })
            }
          >
            <Image
              source={{ uri: category.image }}
              style={styles.image}
            />
            <Text style={styles.categoryName}>{category.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  heading: {
    fontFamily: 'Lato-Semibold',
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 64,
    letterSpacing: 0.72,
    textTransform: 'uppercase',
    color: '#000',
  },
  scrollViewContent: {
    gap: ms(12),
  },
  category: {
    width: ms(120),
    height: ms(165),
    alignItems: 'center',
    marginRight: ms(12),
    borderRadius: ms(8),
    gap: vs(8),
    opacity: 1,
  },
  image: {
    width: ms(120),
    height: ms(120),
    borderRadius: ms(8),
    objectFit: 'contain',
  },
  categoryName: {
    fontFamily: 'Lato-Medium',
    fontWeight: '500',
    fontSize: ms(14),
    lineHeight: vs(18),
    letterSpacing: 0,
    textAlign: 'center',
    textAlignVertical: 'center',
    color: '#000',
  },
});

export default AcademyTopCategories;
