import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

const CustomRadioButton = ({
  value,
  status,
  onPress,
  color = '#000',
  label,
  // Optional styling props
  reverse = false,
  size = 20,
  labelStyle = {},
  containerStyle = {},
  radioButtonStyle = {},
  innerCircleStyle = {},
  disabled = false,
  labelColor = 'black',
  fontSize = 16,
  spacing = 5,
  borderWidth = 2,
  testID
}) => {
  const containerDirection = reverse ? 'row-reverse' : 'row';
  const marginStyle = reverse
    ? { marginLeft: spacing }
    : { marginRight: spacing };

  return (
    <TouchableOpacity
      style={[
        styles.radioButtonContainer,
        { flexDirection: containerDirection },
        containerStyle,
        disabled && styles.disabled
      ]}
      onPress={() => !disabled && onPress(value)}
      disabled={disabled}
      testID={testID}
    >
      <View style={[
        styles.radioButton,
        {
          borderColor: disabled ? '#ccc' : color,
          height: size,
          width: size,
          borderRadius: size / 2,
          borderWidth: borderWidth
        },
        marginStyle,
        radioButtonStyle
      ]}>
        {status && (
          <View style={[
            styles.radioButtonInner,
            {
              backgroundColor: disabled ? '#ccc' : color,
              height: size * 0.5,
              width: size * 0.5,
              borderRadius: (size * 0.5) / 2
            },
            innerCircleStyle
          ]} />
        )}
      </View>
      <Text style={[
        styles.radioButtonLabel,
        {
          fontSize: fontSize,
          color: disabled ? '#ccc' : labelColor
        },
        labelStyle
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  radioButtonContainer: {
    alignItems: 'center',
  },
  radioButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    // Dynamic styles applied inline
  },
  radioButtonLabel: {
    // Dynamic styles applied inline
  },
  disabled: {
    opacity: 0.6,
  },
});

export default CustomRadioButton;
