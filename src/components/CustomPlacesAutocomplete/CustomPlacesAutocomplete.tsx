import React, { useState } from 'react';
import { View, TextInput, FlatList, TouchableOpacity, Text, ActivityIndicator } from 'react-native';
import { getPlaceAutocompleteResults, getPlaceDetailsById } from '../../helpers/locationHelper';
import { styles } from "./styles";

interface CustomPlacesAutocompleteProps {
  value: string;
  onChangeText: (text: string) => void;
  onSelect: (result: { description: string; lat: number; lng: number }) => void;
  placeholder?: string;
  style?: any;
}

const CustomPlacesAutocomplete: React.FC<CustomPlacesAutocompleteProps> = ({
  value,
  onChangeText,
  onSelect,
  placeholder = 'City Name',
  style,
}) => {
  const [predictions, setPredictions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const handleChange = async (text: string) => {
    onChangeText(text);
    if (text.length >= 3) {
      setLoading(true);
      const results = await getPlaceAutocompleteResults(text);
      setPredictions(results);
      setShowDropdown(true);
      setLoading(false);
    } else {
      setPredictions([]);
      setShowDropdown(false);
    }
  };

  const handleSelect = async (prediction: any) => {
    setLoading(true);
    const coords = await getPlaceDetailsById(prediction.place_id);
    setLoading(false);
    if (coords) {
      onSelect({
        description: prediction.description,
        lat: coords.lat,
        lng: coords.lng,
      });
      setShowDropdown(false);
    }
  };

  return (
    <View style={[styles.container, style]}>
      <TextInput
        value={value}
        onChangeText={handleChange}
        placeholder={placeholder}
        style={styles.input}
        autoCorrect={false}
        autoCapitalize="none"
      />
      {loading && <ActivityIndicator style={styles.loading} size="small" color="#0EA5E9" />}
      {showDropdown && predictions.length > 0 && (
        <View style={styles.dropdown}>
          <FlatList
            data={predictions}
            keyExtractor={item => item.place_id}
            renderItem={({ item }) => (
              <TouchableOpacity style={styles.item} onPress={() => handleSelect(item)}>
                <Text style={styles.itemText}>{item.description}</Text>
              </TouchableOpacity>
            )}
            keyboardShouldPersistTaps="handled"
          />
        </View>
      )}
    </View>
  );
};

export default CustomPlacesAutocomplete; 