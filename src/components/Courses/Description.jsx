import { View, Text, StyleSheet } from 'react-native';
import HTML from 'react-native-render-html';

export default function CourseDescription({ course }) {
  const { description, cancellationPolicy } = course;
  return (
    <View style={styles.container4}>
      <Text style={[styles.heading]}>DESCRIPTION</Text>
      <HTML tagsStyles={{
        p: { margin: 0, paddingHorizontal: "2%", color: "#000", fontFamily:"Lato-Regular"
      }, 
        li: { margin: 0, paddingHorizontal: "2%", color: "#000", alignItems: 'center'},
        ol: { marginHorizontal: "5%" , alignItems: 'center', padding:0},
        ul: { marginHorizontal: "5%" , alignItems: 'center', padding:0}, 
        h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
      }} source={{ html: description }} />

      {cancellationPolicy ? (
        <View style={styles.cancellationPolicy}>
          <Text style={{ color: "#fff", alignSelf: "center"}}>CANCELLATION POLICY</Text>
          <HTML tagsStyles={{ 
            p: { margin: 0, padding: "2%", color: "#fff",fontFamily:"Lato-Bold" }, 
            li: { margin: 0, padding: "2%", color: "#fff" },
            h2: { margin: 0, padding: 0, color: "#000", fontSize: 15 },
             }} source={{ html: cancellationPolicy }} />
        </View>
      ) : null}

    </View>
  );
}

const styles = StyleSheet.create({
  container4: {
    marginVertical: "5%",
    marginHorizontal: "5%",
    gap: 10,
  },
  heading: {
    fontSize: 18,
    color: 'black',
    fontFamily:"Lato-Bold"
  },
  cancellationPolicy: {
    height: 'auto',
    backgroundColor: '#000',
    borderRadius: 5,
    paddingHorizontal: "5%",
    paddingVertical: "2%"
  },
});
