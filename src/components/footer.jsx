import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, TextInput, Linking, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NEXT_PUBLIC_BASE_URL, NEXT_PUBLIC_SAME_AS } from '@env';
import { Divider } from 'react-native-paper';
const plus = require("../assets/plus.png");
const minus = require("../assets/minus.png");

const handleRegisterNow = () => {
  Linking.openURL('https://coach.khelcoach.com/profile/basic_details');
};

const handleCoachLogin = () => {
  Linking.openURL('https://coach.khelcoach.com/login');
};
const handleWebOpen = () => {
  Linking.openURL('https://www.khelcoach.com/');
};

const handlePhonePress = () => {
  Linking.openURL('tel:+919267986189');
};

const handleSocialLink = (url) => {
  Linking.canOpenURL(url)
    .then((supported) => {
      if (supported) {
        Linking.openURL(url);
      } else {
        Alert.alert("Error", "Unable to open the link.");
      }
    })
    .catch((err) => console.error("An error occurred:", err));
};

export default function Footer() {
  const navigation = useNavigation();
  const [expandedSections, setExpandedSections] = useState([]);
  const [email, setEmail] = useState('');
  const [errorMessage, setErrorMessage] = useState(false);
  const [errorMessageMail, setErrorMessageMail] = useState('');
  const [successAlert, setSuccessAlert] = useState(false);
  const [successMsg, setSuccessMsg] = useState('');
  const [errorAlert, setErrorAlert] = useState(false);

  const toggleSection = (section) => {
    if (expandedSections.includes(section)) {
      setExpandedSections(expandedSections.filter((item) => item !== section));
    } else {
      setExpandedSections([...expandedSections, section]);
    }
  };

  const isSectionExpanded = (section) => {
    return expandedSections.includes(section);
  };

  const navigationDetail = {
    contact: [
      { name: 'Umn Khel Shiksha Private Limited,Vasant Vihar,Basant Lok Complex,Road 21,New Delhi-110057', href: '#', image: require('../assets/location.png') },
      { name: '+91 92679 86189', href: '#', image: require('../assets/phone.png'), action: handlePhonePress },
      { name: 'https://khelcoach.com', href: 'https://khelcoach.com', image: require('../assets/web.png') , action: handleWebOpen},
    ],
    quick: [
      { name: 'About Us', href: '#', link: "AboutUs" },
      { name: "FAQ's", href: '#', link: "FAQ" },
      { name: 'Term of Service', href: '#', link: "TermsOfService" },
      { name: 'Privacy Policy', href: '#', link: "PrivacyPolicy" },
      { name: 'Customer Grievance', href: '#', link: "CustomerGrievance" },
      { name: 'Register as a Coach', href: '#', action: handleRegisterNow },
      { name: 'Already a registered Coach, login', href: '#', action: handleCoachLogin },
    ],
    social: [
      { name: 'Facebook', href: 'https://facebook.com/61568900504191', image: require('../assets/Facebook.png') },
      { name: 'Instagram', href: 'https://www.instagram.com/khelcoach/profilecard/?igsh=MW9wM3JwcDR3cmp1cA==', image: require('../assets/Instagram.png') },
      { name: 'Linkedin', href: 'https://www.linkedin.com/company/*********/admin/settings/manage-admins/', image: require('../assets/Linkedin.png') },
      { name: 'Twitter', href: 'https://x.com/khelcoach', image: require('../assets/xicon.png') },
    ],
  };

  const handleSocaialLink = (url) => {
    Linking.openURL(url);
  };

  const emailRegExp = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  const handleNewsLetterSubscription = async (e) => {
    e.preventDefault();
    try {
      if (!emailRegExp.test(email)) {
        setErrorMessage(true);
        return;
      }
      setErrorMessage(false);

      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({ email }),
      };

      const response = await fetch(
        `${NEXT_PUBLIC_BASE_URL}/api/newsletter`,
        requestOptions
      );
      const result = await response.json();
      if (result.error) {
        setErrorAlert(true);
        setErrorMessageMail(result?.error)
        setTimeout(() => {
          setErrorAlert(false);
        }, 3000);
      } else {
        setEmail("");
        setSuccessAlert(true);
        setTimeout(() => {
          setSuccessAlert(false);
        }, 3000);
      }
    } catch (error) {
      console.log(error, "error");
    }
  };

  return (
    <View style={{ backgroundColor: 'white', borderTopWidth: 1 }}>
      <View style={{ marginHorizontal: 'auto', maxWidth: 800, padding: 20, gap: 10 }}>
        <View style={styles.containers}>
          <Text style={{ fontSize: 20, color: 'black', fontFamily: "Lato-Bold" }}>Newsletter</Text>
          <Text style={{ marginTop: 10, fontSize: 15, color: 'black', fontFamily: "Lato-Regular" }}>
            Subscribe To Our Updates
          </Text>
          <View style={{ marginTop: 10 }}>
            <TextInput
              placeholder="Your Email"
              placeholderTextColor="#9CA3AF"
              style={styles.emailInput}
              value={email}
              onChangeText={setEmail}
            />
            <TouchableOpacity
              style={styles.subscribeButton}
              onPress={handleNewsLetterSubscription}
            >
              <Text style={styles.subscribeButtonText}>SUBSCRIBE</Text>
            </TouchableOpacity>
          </View>
          {errorMessage && <Text style={styles.errorText}>Invalid email address.</Text>}
          {successAlert && <Text style={styles.successText}>Successfully subscribed!</Text>}
          {errorAlert && <Text style={styles.errorText}>{errorMessageMail}</Text>}
          <View style={{ marginTop: 20 }}>
            <Text style={{ fontSize: 15, color: 'black', fontFamily: "Lato-Regular" }}>We accept:</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 5 }}>
              <Image source={require('../assets/Visa.png')} />
              <Image source={require('../assets/MasterCard.png')} />
              <Image source={require('../assets/AmericanExpress.png')} />
            </View>
          </View>
          <Divider />
        </View>
        <View style={styles.containers}>
          <TouchableOpacity onPress={() => toggleSection('contact')}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ fontSize: 16, color: '#000000', fontFamily: "Lato-Regular" }}>Contact Info</Text>
              <Image source={isSectionExpanded('contact') ? minus : plus} style={styles.icon} />
            </View>
          </TouchableOpacity>
          {isSectionExpanded('contact') && (
            <View style={{ marginVertical: 10, gap: 10 }}>
              {navigationDetail.contact.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}
                  activeOpacity={item.action || item.link ? 0.5 : 1}
                  onPress={
                    item.action
                      ? item.action
                      : item.link
                      ? () => navigation.navigate(item.link)
                      : undefined
                  }
                >
                  <Image source={item.image} />
                  <Text style={{ fontSize: 14, color: '#666', fontFamily: "Lato-Regular" }}>
                    {item.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        <Divider />
        <View style={styles.containers}>
          <TouchableOpacity onPress={() => toggleSection('quick')}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ fontSize: 16, color: 'black', fontFamily: "Lato-Regular" }}>Quick Links</Text>
              <Image source={isSectionExpanded('quick') ? minus : plus} style={styles.icon} />
            </View>
          </TouchableOpacity>
          {isSectionExpanded('quick') && (
            <View style={{ marginTop: 10 }}>
              {navigationDetail.quick.map((item, index) => (
                <TouchableOpacity key={index} onPress={() => {
                  if (item.action) {
                    item.action();
                  } else if (item.link) {
                    navigation.navigate(item.link);
                  }
                }}>
                  <Text style={{ fontSize: 14, color: '#666', marginBottom: 8, fontFamily: "Lato-Regular" }}>{item.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        <Divider />
        <View style={styles.containers}>
          <TouchableOpacity onPress={() => toggleSection('social')}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ fontSize: 16, color: 'black', fontFamily: "Lato-Regular" }}>Social Media</Text>
              <Image source={isSectionExpanded('social') ? minus : plus} style={styles.icon} />
            </View>
          </TouchableOpacity>
          {isSectionExpanded('social') && (
            <View style={{ marginTop: 10, display: "flex", flexDirection: 'row', alignItems: 'center', justifyContent: "space-evenly" }}>
              {navigationDetail.social.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => handleSocaialLink(item.href)}
                  style={{ display: "flex", flexDirection: 'column', alignItems: 'center' }}
                >
                  <Image source={item.image} style={{ width: 20, height: 20 }} />
                  <Text style={{ fontSize: 16, color: '#666', marginLeft: 8, fontFamily: "Lato-Regular" }}>
                    {item.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
        <Divider />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  containers: {
    overflow: 'hidden',
    height: null,
    marginVertical: "1%",
  },
  icon: {
    width: 12,
    height: 10,
  },
  emailInput: {
    width: '100%',
    minHeight: 40,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontFamily: "Lato-Regular",
    color:"#000"
  },
  subscribeButton: {
    width: '100%',
    backgroundColor: '#E31F26',
    borderRadius: 4,
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  subscribeButtonText: {
    color: '#FAFBFCFF',
    fontFamily: "Lato-Bold"
  },
  errorText: {
    color: 'red',
    marginTop: 10,
    fontFamily: "Lato-Regular"
  },
  successText: {
    color: 'green',
    marginTop: 10,
    fontFamily: "Lato-Regular"
  }
});
