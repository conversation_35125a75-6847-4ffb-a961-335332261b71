import React, {createContext, useContext, useState, useEffect} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createDrawerNavigator} from '@react-navigation/drawer';
import HomePage from './src/screens/HomePage/HomePage';
import SignIn from './src/screens/SignIn';
import SignUp from './src/screens/SignUp';
import Courses from './src/screens/Courses';
import ThankYou from './src/screens/ThankYou';
import PlayerProfile from './src/screens/PlayerProfile/PlayerProfile';
import CoachProfile from './src/screens/CoachProfile';
import ListingCards from './src/screens/ListingCards/ListingCards';
import {AuthProvider} from './src/Context/AuthContext';
import Filter from './src/components/Filters/Filter';
import Collection from './src/components/Filters/Collection';
import FAQ from './src/screens/FAQ';
import PrivacyPolicy from './src/screens/PrivacyPolicy';
import CustomerGrievance from './src/screens/CustomerGrievance';
import Header from './src/components/header';
import BookingDetails from './src/screens/BookingDetails';
import RegisterAsCoach from './src/screens/RegisterAsCoach';
import ReadMore from './src/screens/ReadMore';
import Transactions from './src/screens/Transactions';
import {PaperProvider} from 'react-native-paper';
import TimePicker from './src/screens/TimePicker';
import QrScanner from './src/screens/QrScanner';
import AboutUs from './src/screens/AboutUs';
import Support from './src/screens/Support';
import TermsOfService from './src/screens/TermsOfService';
import Invoice from './src/screens/Invoice';
import CoachInvoice from './src/screens/CoachInvoice';
import OrderSummary from './src/screens/OrderSummary';
import ForgetPassword from './src/screens/ForgetPassword';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from './src/Context/AuthContext';
import {NEXT_PUBLIC_BASE_URL, JWT_SECRET} from '@env';
import Contact from './src/screens/Contact';
// Import SearchScreen
import SearchScreen from './src/screens/Search/SearchScreen';
import Orientation from 'react-native-orientation-locker';
import { SafeAreaView } from 'react-native-safe-area-context';
import AcademyProfile from './src/screens/AcademyProfile/AcademyProfile';

const Stack = createNativeStackNavigator();

function App(): React.JSX.Element {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const handleDrawerOpen = isOpen => {
    setIsDrawerOpen(isOpen);
  };

  useEffect(() => {
    Orientation.lockToPortrait();
  }, []);
  return (
    <SafeAreaView style={{flex:1}}>
      <PaperProvider>
        <NavigationContainer>
        <AuthProvider>
          <Stack.Navigator initialRouteName="Home">
            <Stack.Screen
              name="Home"
              component={HomePage}
              options={{
                headerShown: false,
              }}
            />
            <Stack.Screen name="SignIn" component={SignIn} />
            <Stack.Screen name="SignUp" component={SignUp} />
            <Stack.Screen
              name="Courses"
              component={Courses}
              initialParams={{courseId: 123}}
            />
            <Stack.Screen name="ThankYou" component={ThankYou} />
            <Stack.Screen name="PlayerProfile" component={PlayerProfile} />
            <Stack.Screen name="CoachProfile" component={CoachProfile} />
            <Stack.Screen name="ListingCards" component={ListingCards} />
            <Stack.Screen name="Collection" component={Collection} />
            <Stack.Screen name="FAQ" component={FAQ} />
            <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicy} />
            <Stack.Screen name="CustomerGrievance" component={CustomerGrievance} />
            <Stack.Screen name="BookingDetails" component={BookingDetails} />
            <Stack.Screen name="RegisterAsCoach" component={RegisterAsCoach} />
            <Stack.Screen name="ReadMore" component={ReadMore} />
            <Stack.Screen name="Transactions" component={Transactions} />
            <Stack.Screen name="TimePicker" component={TimePicker} />
            <Stack.Screen name="AcademyProfile" component={AcademyProfile} />
            <Stack.Screen name="QrScanner" component={QrScanner} />
            <Stack.Screen name="Support" component={Support} />
            <Stack.Screen name="AboutUs" component={AboutUs} />
            <Stack.Screen name="Invoice" component={Invoice} />
            <Stack.Screen name="CoachInvoice" component={CoachInvoice} />
            <Stack.Screen name="OrderSummary" component={OrderSummary} />
            <Stack.Screen name="TermsOfService" component={TermsOfService} />
            <Stack.Screen name='ForgetPassword' component={ForgetPassword} />
            <Stack.Screen name='Contact' component={Contact} />
            <Stack.Screen 
              name="SearchScreen" 
              component={SearchScreen}
              options={{
                title: "Search",
                headerShown: true
              }}
            />
          </Stack.Navigator>
          </AuthProvider>
        </NavigationContainer>
      </PaperProvider>
      </SafeAreaView>
   
  );
}
export default App;

